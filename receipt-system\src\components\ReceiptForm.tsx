'use client';

import { useState, useEffect } from 'react';
import { ReceiptData } from '@/app/page';

interface ReceiptFormProps {
  receiptData: ReceiptData;
  setReceiptData: (data: ReceiptData) => void;
  onPrint: () => void;
}

export function ReceiptForm({ receiptData, setReceiptData, onPrint }: ReceiptFormProps) {
  const [calculationMode, setCalculationMode] = useState<'dollar-to-iraqi' | 'iraqi-to-dollar'>('dollar-to-iraqi');

  // Generate receipt number automatically
  useEffect(() => {
    if (!receiptData.receiptNumber) {
      const now = new Date();
      const receiptNumber = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
      setReceiptData({ ...receiptData, receiptNumber });
    }
  }, []);

  const handleInputChange = (field: keyof ReceiptData, value: string | number) => {
    const updatedData = { ...receiptData, [field]: value };
    
    // Auto-calculate based on mode
    if (field === 'dollarAmount' && calculationMode === 'dollar-to-iraqi') {
      updatedData.iraqiAmount = Number(value) * receiptData.exchangeRate;
    } else if (field === 'iraqiAmount' && calculationMode === 'iraqi-to-dollar') {
      updatedData.dollarAmount = Number(value) / receiptData.exchangeRate;
    } else if (field === 'exchangeRate') {
      if (calculationMode === 'dollar-to-iraqi') {
        updatedData.iraqiAmount = receiptData.dollarAmount * Number(value);
      } else {
        updatedData.dollarAmount = receiptData.iraqiAmount / Number(value);
      }
    }
    
    setReceiptData(updatedData);
  };

  const generateNewReceiptNumber = () => {
    const now = new Date();
    const receiptNumber = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
    setReceiptData({ ...receiptData, receiptNumber });
  };

  return (
    <div className="space-y-6">
      {/* Receipt Number and Date */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            رقم الإيصال
          </label>
          <div className="flex gap-2">
            <input
              type="text"
              value={receiptData.receiptNumber}
              onChange={(e) => handleInputChange('receiptNumber', e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="رقم الإيصال"
            />
            <button
              onClick={generateNewReceiptNumber}
              className="px-3 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
              title="إنشاء رقم جديد"
            >
              🔄
            </button>
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            التاريخ
          </label>
          <input
            type="date"
            value={receiptData.date}
            onChange={(e) => handleInputChange('date', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Project Name */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          اسم المشروع
        </label>
        <input
          type="text"
          value={receiptData.projectName}
          onChange={(e) => handleInputChange('projectName', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="اسم المشروع"
        />
      </div>

      {/* Calculation Mode */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          طريقة الحساب
        </label>
        <div className="flex gap-4">
          <label className="flex items-center">
            <input
              type="radio"
              value="dollar-to-iraqi"
              checked={calculationMode === 'dollar-to-iraqi'}
              onChange={(e) => setCalculationMode(e.target.value as 'dollar-to-iraqi')}
              className="mr-2"
            />
            من الدولار إلى الدينار
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              value="iraqi-to-dollar"
              checked={calculationMode === 'iraqi-to-dollar'}
              onChange={(e) => setCalculationMode(e.target.value as 'iraqi-to-dollar')}
              className="mr-2"
            />
            من الدينار إلى الدولار
          </label>
        </div>
      </div>

      {/* Exchange Rate */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          سعر الصرف (دينار لكل دولار)
        </label>
        <input
          type="number"
          value={receiptData.exchangeRate}
          onChange={(e) => handleInputChange('exchangeRate', Number(e.target.value))}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="1500"
          min="0"
          step="0.01"
        />
      </div>

      {/* Amounts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            المبلغ بالدولار ($)
          </label>
          <input
            type="number"
            value={receiptData.dollarAmount || ''}
            onChange={(e) => handleInputChange('dollarAmount', Number(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="0.00"
            min="0"
            step="0.01"
            disabled={calculationMode === 'iraqi-to-dollar'}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            المبلغ بالدينار العراقي (IQD)
          </label>
          <input
            type="number"
            value={receiptData.iraqiAmount || ''}
            onChange={(e) => handleInputChange('iraqiAmount', Number(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="0"
            min="0"
            step="1"
            disabled={calculationMode === 'dollar-to-iraqi'}
          />
        </div>
      </div>

      {/* Description */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          وصف العملية
        </label>
        <textarea
          value={receiptData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="وصف تفصيلي للعملية..."
          rows={3}
        />
      </div>

      {/* Print Button */}
      <div className="flex justify-center">
        <button
          onClick={onPrint}
          className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold text-lg"
        >
          🖨️ طباعة الإيصال
        </button>
      </div>
    </div>
  );
}
