'use client';

import { useState, useEffect } from 'react';
import { ReceiptData } from '@/app/page';

interface ReceiptFormProps {
  receiptData: ReceiptData;
  setReceiptData: (data: ReceiptData) => void;
  onPrint: () => void;
}

export function ReceiptForm({ receiptData, setReceiptData, onPrint }: ReceiptFormProps) {
  const [calculationMode, setCalculationMode] = useState<'dollar-to-iraqi' | 'iraqi-to-dollar'>('dollar-to-iraqi');

  // Generate receipt number automatically
  useEffect(() => {
    if (!receiptData.receiptNumber) {
      const now = new Date();
      const receiptNumber = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
      setReceiptData({ ...receiptData, receiptNumber });
    }
  }, []);

  const handleInputChange = (field: keyof ReceiptData, value: string | number) => {
    const updatedData = { ...receiptData, [field]: value };
    
    // Auto-calculate based on mode
    if (field === 'dollarAmount' && calculationMode === 'dollar-to-iraqi') {
      updatedData.iraqiAmount = Number(value) * receiptData.exchangeRate;
    } else if (field === 'iraqiAmount' && calculationMode === 'iraqi-to-dollar') {
      updatedData.dollarAmount = Number(value) / receiptData.exchangeRate;
    } else if (field === 'exchangeRate') {
      if (calculationMode === 'dollar-to-iraqi') {
        updatedData.iraqiAmount = receiptData.dollarAmount * Number(value);
      } else {
        updatedData.dollarAmount = receiptData.iraqiAmount / Number(value);
      }
    }
    
    setReceiptData(updatedData);
  };

  const generateNewReceiptNumber = () => {
    const now = new Date();
    const receiptNumber = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
    setReceiptData({ ...receiptData, receiptNumber });
  };

  return (
    <div className="space-y-6">
      {/* Receipt Number and Date */}
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <h3 className="text-lg font-semibold text-blue-800 mb-4 text-center">📋 معلومات الإيصال الأساسية</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-blue-700 mb-2">
              🔢 رقم الإيصال
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                value={receiptData.receiptNumber}
                onChange={(e) => handleInputChange('receiptNumber', e.target.value)}
                className="flex-1 px-3 py-3 border-2 border-blue-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                placeholder="رقم الإيصال"
              />
              <button
                onClick={generateNewReceiptNumber}
                className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-md"
                title="إنشاء رقم جديد"
              >
                🔄
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-blue-700 mb-2">
              📅 التاريخ
            </label>
            <input
              type="date"
              value={receiptData.date}
              onChange={(e) => handleInputChange('date', e.target.value)}
              className="w-full px-3 py-3 border-2 border-blue-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            />
          </div>
        </div>
      </div>

      {/* Project Name */}
      <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
        <label className="block text-sm font-medium text-purple-700 mb-2">
          🏗️ اسم المشروع
        </label>
        <input
          type="text"
          value={receiptData.projectName}
          onChange={(e) => handleInputChange('projectName', e.target.value)}
          className="w-full px-3 py-3 border-2 border-purple-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white"
          placeholder="أدخل اسم المشروع (اختياري)"
        />
      </div>

      {/* Calculation Mode */}
      <div className="bg-green-50 p-4 rounded-lg border border-green-200">
        <label className="block text-sm font-medium text-green-700 mb-3">
          ⚙️ طريقة الحساب
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <label className="flex items-center bg-white p-3 rounded-lg border-2 border-green-300 cursor-pointer hover:bg-green-50 transition-colors">
            <input
              type="radio"
              value="dollar-to-iraqi"
              checked={calculationMode === 'dollar-to-iraqi'}
              onChange={(e) => setCalculationMode(e.target.value as 'dollar-to-iraqi')}
              className="mr-3 w-4 h-4 text-green-600"
            />
            <div>
              <span className="font-medium text-green-800">💵➡️🏛️ من الدولار إلى الدينار</span>
              <p className="text-xs text-green-600 mt-1">أدخل المبلغ بالدولار وسيتم حساب الدينار تلقائياً</p>
            </div>
          </label>
          <label className="flex items-center bg-white p-3 rounded-lg border-2 border-green-300 cursor-pointer hover:bg-green-50 transition-colors">
            <input
              type="radio"
              value="iraqi-to-dollar"
              checked={calculationMode === 'iraqi-to-dollar'}
              onChange={(e) => setCalculationMode(e.target.value as 'iraqi-to-dollar')}
              className="mr-3 w-4 h-4 text-green-600"
            />
            <div>
              <span className="font-medium text-green-800">🏛️➡️💵 من الدينار إلى الدولار</span>
              <p className="text-xs text-green-600 mt-1">أدخل المبلغ بالدينار وسيتم حساب الدولار تلقائياً</p>
            </div>
          </label>
        </div>
      </div>

      {/* Exchange Rate */}
      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
        <label className="block text-sm font-medium text-yellow-700 mb-2">
          📊 سعر الصرف (دينار عراقي لكل دولار أمريكي)
        </label>
        <div className="relative">
          <input
            type="number"
            value={receiptData.exchangeRate}
            onChange={(e) => handleInputChange('exchangeRate', Number(e.target.value))}
            className="w-full px-4 py-3 border-2 border-yellow-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white text-lg font-semibold"
            placeholder="1500"
            min="0"
            step="0.01"
          />
          <span className="absolute left-3 top-3 text-yellow-600 font-medium">د.ع</span>
        </div>
        <p className="text-xs text-yellow-600 mt-2">💡 السعر الحالي للصرف في السوق</p>
      </div>

      {/* Amounts */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg border-2 border-green-200">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">💰 المبالغ المالية</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className={`p-3 rounded-lg border-2 ${calculationMode === 'iraqi-to-dollar' ? 'bg-gray-100 border-gray-300' : 'bg-white border-green-300'}`}>
            <label className="block text-sm font-medium text-green-700 mb-2">
              💵 المبلغ بالدولار الأمريكي ($)
            </label>
            <div className="relative">
              <input
                type="number"
                value={receiptData.dollarAmount || ''}
                onChange={(e) => handleInputChange('dollarAmount', Number(e.target.value))}
                className={`w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 text-lg font-semibold ${
                  calculationMode === 'iraqi-to-dollar'
                    ? 'border-gray-300 bg-gray-100 text-gray-500 cursor-not-allowed'
                    : 'border-green-300 bg-white focus:ring-green-500 focus:border-green-500'
                }`}
                placeholder="0.00"
                min="0"
                step="0.01"
                disabled={calculationMode === 'iraqi-to-dollar'}
              />
              <span className="absolute left-3 top-3 text-green-600 font-medium">$</span>
            </div>
            {calculationMode === 'iraqi-to-dollar' && (
              <p className="text-xs text-gray-500 mt-1">🔒 يتم الحساب تلقائياً</p>
            )}
          </div>

          <div className={`p-3 rounded-lg border-2 ${calculationMode === 'dollar-to-iraqi' ? 'bg-gray-100 border-gray-300' : 'bg-white border-blue-300'}`}>
            <label className="block text-sm font-medium text-blue-700 mb-2">
              🏛️ المبلغ بالدينار العراقي (IQD)
            </label>
            <div className="relative">
              <input
                type="number"
                value={receiptData.iraqiAmount || ''}
                onChange={(e) => handleInputChange('iraqiAmount', Number(e.target.value))}
                className={`w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 text-lg font-semibold ${
                  calculationMode === 'dollar-to-iraqi'
                    ? 'border-gray-300 bg-gray-100 text-gray-500 cursor-not-allowed'
                    : 'border-blue-300 bg-white focus:ring-blue-500 focus:border-blue-500'
                }`}
                placeholder="0"
                min="0"
                step="1"
                disabled={calculationMode === 'dollar-to-iraqi'}
              />
              <span className="absolute left-3 top-3 text-blue-600 font-medium">د.ع</span>
            </div>
            {calculationMode === 'dollar-to-iraqi' && (
              <p className="text-xs text-gray-500 mt-1">🔒 يتم الحساب تلقائياً</p>
            )}
          </div>
        </div>
      </div>

      {/* Description */}
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          📝 وصف العملية والملاحظات
        </label>
        <textarea
          value={receiptData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white resize-none"
          placeholder="أدخل وصفاً تفصيلياً للعملية أو أي ملاحظات إضافية..."
          rows={4}
        />
        <p className="text-xs text-gray-500 mt-2">💡 يمكنك إضافة تفاصيل إضافية حول العملية المالية</p>
      </div>

      {/* Print Button */}
      <div className="flex justify-center pt-4">
        <button
          onClick={onPrint}
          className="px-12 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 font-bold text-lg shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          <span className="flex items-center gap-3">
            🖨️ طباعة الإيصال
            <span className="text-sm bg-blue-500 px-2 py-1 rounded-full">Print</span>
          </span>
        </button>
      </div>

      {/* Additional Info */}
      <div className="bg-blue-50 p-3 rounded-lg border border-blue-200 text-center">
        <p className="text-sm text-blue-700">
          ✅ سيتم طباعة نسختين: الأصل للعميل ونسخة للأرشفة
        </p>
        <p className="text-xs text-blue-600 mt-1">
          تأكد من صحة جميع البيانات قبل الطباعة
        </p>
      </div>
    </div>
  );
}
