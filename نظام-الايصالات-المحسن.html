<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الإيصالات المالية - هيمن كروب</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #e1bee7 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            border: 2px solid #e3f2fd;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 4px solid #1976d2;
            margin-left: 20px;
            object-fit: cover;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .header-text h1 {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #1976d2, #7b1fa2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header-text p {
            font-size: 1.2rem;
            color: #1976d2;
            font-weight: 600;
        }

        .header-info {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            padding: 15px;
            border-radius: 15px;
            border: 2px solid #bbdefb;
            text-align: center;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            border: 2px solid #e3f2fd;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #1976d2, #7b1fa2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            text-align: center;
        }

        .card-header h2 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .card-header p {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border: 2px solid #e9ecef;
        }

        .form-section h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #495057;
            text-align: center;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
        }

        input, textarea, select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
        }

        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #1976d2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
            transform: translateY(-2px);
        }

        .radio-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 10px;
        }

        .radio-option {
            background: white;
            padding: 15px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .radio-option:hover {
            border-color: #1976d2;
            background: #f8f9fa;
        }

        .radio-option.active {
            border-color: #1976d2;
            background: #e3f2fd;
        }

        .radio-option input[type="radio"] {
            width: auto;
            margin-left: 10px;
        }

        .btn {
            background: linear-gradient(135deg, #1976d2, #1565c0);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 15px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(25, 118, 210, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            padding: 8px 15px;
            font-size: 14px;
            width: auto;
            margin: 0;
        }

        .receipt {
            background: white;
            border: 3px solid #333;
            padding: 30px;
            border-radius: 15px;
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-height: 800px;
            overflow-y: auto;
        }

        .receipt-header {
            text-align: center;
            border-bottom: 3px solid #1976d2;
            padding-bottom: 20px;
            margin-bottom: 25px;
        }

        .receipt-logo-section {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
        }

        .receipt-logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 3px solid #1976d2;
            margin-left: 15px;
            object-fit: cover;
        }

        .receipt-title h1 {
            font-size: 1.8rem;
            color: #1976d2;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .receipt-title h2 {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 10px;
        }

        .receipt-type {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 10px;
            border: 2px solid #bbdefb;
        }

        .receipt-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            border: 2px solid #dee2e6;
        }

        .receipt-info h3 {
            text-align: center;
            margin-bottom: 15px;
            color: #495057;
            font-weight: 700;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
        }

        .receipt-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border-right: 4px solid #1976d2;
        }

        .amount-section {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            border: 3px solid #bbdefb;
        }

        .amount-section h3 {
            text-align: center;
            margin-bottom: 20px;
            color: #1976d2;
            font-weight: 700;
            font-size: 1.3rem;
        }

        .amount-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border-right: 4px solid #28a745;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .amount-total {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 20px;
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
        }

        .signatures {
            margin-top: 30px;
            padding-top: 25px;
            border-top: 3px solid #1976d2;
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
        }

        .signatures h4 {
            text-align: center;
            margin-bottom: 20px;
            color: #495057;
            font-weight: 700;
        }

        .signature-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .signature-box {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #dee2e6;
        }

        .signature-line {
            border-bottom: 2px solid #495057;
            height: 50px;
            margin-bottom: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .receipt-footer {
            margin-top: 25px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            background: #e3f2fd;
            padding: 20px;
            border-radius: 15px;
        }

        .duplicate {
            margin-top: 30px;
            padding-top: 25px;
            border-top: 3px dashed #6c757d;
        }

        .duplicate-header {
            text-align: center;
            background: #fff3cd;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #ffeaa7;
            margin-bottom: 20px;
        }

        .duplicate-content {
            background: #f8f9fa;
            padding: 25px;
            border: 2px solid #6c757d;
            border-radius: 15px;
            transform: scale(0.9);
        }

        .footer {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            border: 2px solid #e3f2fd;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 25px;
            margin-bottom: 25px;
        }

        .footer-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #dee2e6;
            text-align: center;
        }

        .footer-card h4 {
            margin-bottom: 10px;
            font-weight: 700;
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }

            .main-grid {
                display: none;
            }

            .header, .footer {
                display: none;
            }

            .receipt {
                border: 2px solid #000;
                page-break-inside: avoid;
                box-shadow: none;
            }
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .radio-group {
                grid-template-columns: 1fr;
            }

            .footer-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <img src="274989432_110600838240263_3980677387415104690_n.jpg" alt="هيمن كروب" class="logo"
                     onerror="this.style.display='none'">
                <div class="header-text">
                    <h1>نظام إدارة الإيصالات المالية</h1>
                    <p>هيمن كروب - نظام صرف الفواتير الاحترافي</p>
                </div>
            </div>
            <div class="header-info">
                <p style="color: #1976d2; font-weight: 600; font-size: 1.1rem;">
                    💼 نظام متطور لإدارة الإيصالات المالية وتحويل العملات
                </p>
                <p style="color: #666; font-size: 0.9rem; margin-top: 5px;">
                    Professional Financial Receipt Management System
                </p>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-grid">
            <!-- Form Section -->
            <div class="card">
                <div class="card-header">
                    <h2>📝 إدخال بيانات الإيصال</h2>
                    <p>أدخل جميع البيانات المطلوبة لإنشاء الإيصال</p>
                </div>

                <!-- Receipt Number and Date -->
                <div class="form-section">
                    <h3>📋 معلومات الإيصال الأساسية</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="receiptNumber">🔢 رقم الإيصال</label>
                            <div style="display: flex; gap: 10px;">
                                <input type="text" id="receiptNumber" placeholder="رقم الإيصال">
                                <button type="button" class="btn-secondary" onclick="generateReceiptNumber()">🔄</button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="date">📅 التاريخ</label>
                            <input type="date" id="date">
                        </div>
                    </div>
                </div>

                <!-- Project Name -->
                <div class="form-section">
                    <div class="form-group">
                        <label for="projectName">🏗️ اسم المشروع</label>
                        <input type="text" id="projectName" placeholder="أدخل اسم المشروع (اختياري)">
                    </div>
                </div>

                <!-- Calculation Mode -->
                <div class="form-section">
                    <h3>⚙️ طريقة الحساب</h3>
                    <div class="radio-group">
                        <div class="radio-option active" onclick="setCalculationMode('dollar-to-iraqi')">
                            <input type="radio" name="calculationMode" value="dollar-to-iraqi" checked>
                            <div>
                                <strong>💵➡️🏛️ من الدولار إلى الدينار</strong>
                                <p style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                                    أدخل المبلغ بالدولار وسيتم حساب الدينار تلقائياً
                                </p>
                            </div>
                        </div>
                        <div class="radio-option" onclick="setCalculationMode('iraqi-to-dollar')">
                            <input type="radio" name="calculationMode" value="iraqi-to-dollar">
                            <div>
                                <strong>🏛️➡️💵 من الدينار إلى الدولار</strong>
                                <p style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                                    أدخل المبلغ بالدينار وسيتم حساب الدولار تلقائياً
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Exchange Rate -->
                <div class="form-section">
                    <div class="form-group">
                        <label for="exchangeRate">📊 سعر الصرف (دينار عراقي لكل دولار أمريكي)</label>
                        <input type="number" id="exchangeRate" value="1500" min="0" step="0.01"
                               placeholder="1500" style="text-align: center; font-weight: bold;">
                        <p style="font-size: 0.8rem; color: #666; margin-top: 5px; text-align: center;">
                            💡 السعر الحالي للصرف في السوق
                        </p>
                    </div>
                </div>

                <!-- Amounts -->
                <div class="form-section">
                    <h3>💰 المبالغ المالية</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="dollarAmount">💵 المبلغ بالدولار الأمريكي ($)</label>
                            <input type="number" id="dollarAmount" placeholder="0.00" min="0" step="0.01"
                                   style="text-align: center; font-weight: bold;">
                        </div>

                        <div class="form-group">
                            <label for="iraqiAmount">🏛️ المبلغ بالدينار العراقي (IQD)</label>
                            <input type="number" id="iraqiAmount" placeholder="0" min="0" step="1"
                                   style="text-align: center; font-weight: bold;" disabled>
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div class="form-section">
                    <div class="form-group">
                        <label for="description">📝 وصف العملية والملاحظات</label>
                        <textarea id="description" rows="4"
                                  placeholder="أدخل وصفاً تفصيلياً للعملية أو أي ملاحظات إضافية..."></textarea>
                        <p style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                            💡 يمكنك إضافة تفاصيل إضافية حول العملية المالية
                        </p>
                    </div>
                </div>

                <!-- Print Button -->
                <button type="button" class="btn" onclick="printReceipt()">
                    🖨️ طباعة الإيصال
                </button>

                <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; margin-top: 15px; text-align: center; border: 2px solid #bbdefb;">
                    <p style="color: #1976d2; font-weight: 600; font-size: 0.9rem;">
                        ✅ سيتم طباعة نسختين: الأصل للعميل ونسخة للأرشفة
                    </p>
                    <p style="color: #666; font-size: 0.8rem; margin-top: 5px;">
                        تأكد من صحة جميع البيانات قبل الطباعة
                    </p>
                </div>
            </div>

            <!-- Preview Section -->
            <div class="card">
                <div class="card-header" style="background: linear-gradient(135deg, #28a745, #1976d2);">
                    <h2>👁️ معاينة الإيصال</h2>
                    <p>معاينة مباشرة للإيصال قبل الطباعة</p>
                </div>

                <div id="receiptPreview">
                    <!-- Receipt content will be generated here -->
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div style="text-align: center; margin-bottom: 25px;">
                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 15px;">
                    <img src="274989432_110600838240263_3980677387415104690_n.jpg" alt="هيمن كروب"
                         style="width: 50px; height: 50px; border-radius: 50%; border: 2px solid #1976d2; margin-left: 15px; object-fit: cover;"
                         onerror="this.style.display='none'">
                    <div>
                        <h3 style="color: #1976d2; font-size: 1.3rem; margin-bottom: 5px;">هيمن كروب</h3>
                        <p style="color: #666; font-size: 0.9rem;">HEMEN GROUP</p>
                    </div>
                </div>
            </div>

            <div class="footer-grid">
                <div class="footer-card" style="background: #e3f2fd; border-color: #bbdefb;">
                    <h4 style="color: #1976d2;">🏢 معلومات الشركة</h4>
                    <p style="color: #1976d2; font-size: 0.9rem;">شركة هيمن كروب للخدمات المالية</p>
                    <p style="color: #666; font-size: 0.8rem;">نظام إدارة الإيصالات الاحترافي</p>
                </div>

                <div class="footer-card" style="background: #e8f5e8; border-color: #c8e6c9;">
                    <h4 style="color: #28a745;">⚡ الميزات</h4>
                    <p style="color: #28a745; font-size: 0.9rem;">حساب تلقائي للعملات</p>
                    <p style="color: #666; font-size: 0.8rem;">طباعة احترافية مع نسخة أرشفة</p>
                </div>

                <div class="footer-card" style="background: #f3e5f5; border-color: #e1bee7;">
                    <h4 style="color: #7b1fa2;">🔒 الأمان</h4>
                    <p style="color: #7b1fa2; font-size: 0.9rem;">نظام آمن ومحمي</p>
                    <p style="color: #666; font-size: 0.8rem;">حفظ تلقائي للبيانات</p>
                </div>
            </div>

            <div style="border-top: 2px solid #dee2e6; padding-top: 20px; text-align: center;">
                <p style="color: #666; font-size: 0.9rem;">
                    © 2024 هيمن كروب - جميع الحقوق محفوظة | HEMEN GROUP - All Rights Reserved
                </p>
                <p style="color: #999; font-size: 0.8rem; margin-top: 5px;">
                    نظام إدارة الإيصالات المالية الاحترافي v2.0
                </p>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let calculationMode = 'dollar-to-iraqi';

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Set today's date
            document.getElementById('date').value = new Date().toISOString().split('T')[0];

            // Generate initial receipt number
            generateReceiptNumber();

            // Add event listeners
            setupEventListeners();

            // Initial preview update
            updatePreview();
        });

        function setupEventListeners() {
            // Add change listeners to all form inputs
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('input', handleInputChange);
                input.addEventListener('change', handleInputChange);
            });
        }

        function setCalculationMode(mode) {
            calculationMode = mode;

            // Update radio buttons
            document.querySelectorAll('input[name="calculationMode"]').forEach(radio => {
                radio.checked = radio.value === mode;
            });

            // Update visual state
            document.querySelectorAll('.radio-option').forEach(option => {
                option.classList.remove('active');
            });
            event.currentTarget.classList.add('active');

            // Update input states
            const dollarInput = document.getElementById('dollarAmount');
            const iraqiInput = document.getElementById('iraqiAmount');

            if (mode === 'dollar-to-iraqi') {
                dollarInput.disabled = false;
                iraqiInput.disabled = true;
                dollarInput.style.background = 'white';
                iraqiInput.style.background = '#f8f9fa';
            } else {
                dollarInput.disabled = true;
                iraqiInput.disabled = false;
                dollarInput.style.background = '#f8f9fa';
                iraqiInput.style.background = 'white';
            }

            // Recalculate
            handleInputChange();
        }

        function handleInputChange() {
            const exchangeRate = parseFloat(document.getElementById('exchangeRate').value) || 1500;

            if (calculationMode === 'dollar-to-iraqi') {
                const dollarAmount = parseFloat(document.getElementById('dollarAmount').value) || 0;
                document.getElementById('iraqiAmount').value = Math.round(dollarAmount * exchangeRate);
            } else {
                const iraqiAmount = parseFloat(document.getElementById('iraqiAmount').value) || 0;
                document.getElementById('dollarAmount').value = (iraqiAmount / exchangeRate).toFixed(2);
            }

            // Update preview
            updatePreview();
        }

        function generateReceiptNumber() {
            const now = new Date();
            const year = now.getFullYear();
            const month = (now.getMonth() + 1).toString().padStart(2, '0');
            const day = now.getDate().toString().padStart(2, '0');
            const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

            const receiptNumber = `${year}${month}${day}-${random}`;
            document.getElementById('receiptNumber').value = receiptNumber;
            updatePreview();
        }

        function formatNumber(num) {
            return new Intl.NumberFormat('ar-IQ').format(num);
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-IQ', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        function updatePreview() {
            const receiptNumber = document.getElementById('receiptNumber').value || '---';
            const date = document.getElementById('date').value;
            const projectName = document.getElementById('projectName').value;
            const dollarAmount = parseFloat(document.getElementById('dollarAmount').value) || 0;
            const iraqiAmount = parseFloat(document.getElementById('iraqiAmount').value) || 0;
            const exchangeRate = parseFloat(document.getElementById('exchangeRate').value) || 1500;
            const description = document.getElementById('description').value;

            const receiptHTML = `
                <div class="receipt">
                    <div class="receipt-header">
                        <div class="receipt-logo-section">
                            <img src="274989432_110600838240263_3980677387415104690_n.jpg" alt="هيمن كروب" class="receipt-logo"
                                 onerror="this.style.display='none'">
                            <div class="receipt-title">
                                <h1>هيمن كروب</h1>
                                <h2>HEMEN GROUP</h2>
                            </div>
                        </div>
                        <div class="receipt-type">
                            <p style="color: #1976d2; font-weight: 700; font-size: 1rem;">💰 إيصال صرف مالي</p>
                            <p style="color: #666; font-size: 0.8rem;">Financial Receipt</p>
                        </div>
                    </div>

                    <div class="receipt-info">
                        <h3>📋 معلومات الإيصال</h3>
                        <div class="receipt-row">
                            <span style="font-weight: 600; color: #1976d2;">🔢 رقم الإيصال:</span>
                            <span style="font-family: monospace; background: #e3f2fd; padding: 5px 10px; border-radius: 5px;">${receiptNumber}</span>
                        </div>
                        <div class="receipt-row">
                            <span style="font-weight: 600; color: #28a745;">📅 التاريخ:</span>
                            <span>${formatDate(date)}</span>
                        </div>
                        ${projectName ? `
                        <div class="receipt-row">
                            <span style="font-weight: 600; color: #7b1fa2;">🏗️ المشروع:</span>
                            <span>${projectName}</span>
                        </div>
                        ` : ''}
                    </div>

                    <div class="amount-section">
                        <h3>💰 تفاصيل المبلغ المالي</h3>
                        <div class="amount-row">
                            <span style="font-weight: 600;">💵 المبلغ بالدولار:</span>
                            <span style="color: #28a745; font-weight: 700; font-size: 1.1rem;">$${formatNumber(dollarAmount)}</span>
                        </div>
                        <div class="amount-row">
                            <span style="font-weight: 600;">📊 سعر الصرف:</span>
                            <span style="color: #1976d2; font-weight: 700; font-size: 1.1rem;">${formatNumber(exchangeRate)} د.ع</span>
                        </div>
                        <div class="amount-total">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="font-weight: 700; font-size: 1.2rem;">🏛️ المبلغ الإجمالي بالدينار:</span>
                                <span style="font-weight: 700; font-size: 1.3rem;">${formatNumber(iraqiAmount)} د.ع</span>
                            </div>
                        </div>
                    </div>

                    ${description ? `
                    <div style="margin: 25px 0; background: #f8f9fa; padding: 20px; border-radius: 15px; border: 2px solid #dee2e6;">
                        <h4 style="margin-bottom: 15px; color: #495057; font-weight: 700;">📝 وصف العملية:</h4>
                        <p style="background: white; padding: 15px; border-radius: 10px; color: #666; line-height: 1.6;">${description}</p>
                    </div>
                    ` : ''}

                    <div class="signatures">
                        <h4>✍️ التوقيعات والاعتماد</h4>
                        <div class="signature-row">
                            <div class="signature-box">
                                <div class="signature-line"></div>
                                <p style="font-weight: 600; color: #495057;">👤 المستلم</p>
                                <p style="font-size: 0.8rem; color: #666;">Receiver</p>
                                <p style="font-size: 0.7rem; color: #999; margin-top: 5px;">التوقيع والتاريخ</p>
                            </div>
                            <div class="signature-box">
                                <div class="signature-line"></div>
                                <p style="font-weight: 600; color: #495057;">👨‍💼 المسؤول</p>
                                <p style="font-size: 0.8rem; color: #666;">Authorized</p>
                                <p style="font-size: 0.7rem; color: #999; margin-top: 5px;">التوقيع والختم</p>
                            </div>
                        </div>
                    </div>

                    <div class="receipt-footer">
                        <p style="color: #1976d2; font-weight: 600; font-size: 1rem;">✅ هذا الإيصال صالح للأرشفة والمراجعة</p>
                        <p style="color: #666; font-size: 0.8rem; margin-top: 5px;">This receipt is valid for archiving and review</p>
                        <p style="color: #999; font-size: 0.7rem; margin-top: 10px;">تم الإنشاء بواسطة نظام هيمن كروب المالي</p>
                    </div>

                    <!-- Duplicate Copy -->
                    <div class="duplicate">
                        <div class="duplicate-header">
                            <p style="color: #856404; font-weight: 700; font-size: 1rem;">📋 نسخة للأرشفة - Archive Copy</p>
                            <p style="color: #856404; font-size: 0.8rem; margin-top: 5px;">يرجى الاحتفاظ بهذه النسخة للمراجعة</p>
                        </div>

                        <div class="duplicate-content">
                            <div style="text-align: center; margin-bottom: 20px; background: white; padding: 15px; border-radius: 10px;">
                                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
                                    <img src="274989432_110600838240263_3980677387415104690_n.jpg" alt="هيمن كروب"
                                         style="width: 40px; height: 40px; border-radius: 50%; border: 2px solid #6c757d; margin-left: 10px; object-fit: cover;"
                                         onerror="this.style.display='none'">
                                    <div>
                                        <h3 style="color: #495057; font-size: 1.1rem; margin-bottom: 3px;">هيمن كروب</h3>
                                        <p style="color: #6c757d; font-size: 0.8rem;">HEMEN GROUP</p>
                                    </div>
                                </div>
                                <p style="color: #6c757d; font-size: 0.7rem; background: #f8f9fa; padding: 5px 10px; border-radius: 5px;">إيصال صرف - نسخة الأرشفة</p>
                            </div>

                            <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 15px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 8px; padding-bottom: 5px; border-bottom: 1px solid #dee2e6;">
                                    <span style="font-weight: 600; color: #495057; font-size: 0.9rem;">رقم الإيصال:</span>
                                    <span style="font-family: monospace; background: #f8f9fa; padding: 3px 8px; border-radius: 3px; font-size: 0.8rem;">${receiptNumber}</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 8px; padding-bottom: 5px; border-bottom: 1px solid #dee2e6;">
                                    <span style="font-weight: 600; color: #495057; font-size: 0.9rem;">التاريخ:</span>
                                    <span style="font-size: 0.9rem;">${formatDate(date)}</span>
                                </div>
                                ${projectName ? `
                                <div style="display: flex; justify-content: space-between; margin-bottom: 8px; padding-bottom: 5px; border-bottom: 1px solid #dee2e6;">
                                    <span style="font-weight: 600; color: #495057; font-size: 0.9rem;">المشروع:</span>
                                    <span style="font-size: 0.9rem;">${projectName}</span>
                                </div>
                                ` : ''}
                                <div style="display: flex; justify-content: space-between; font-weight: 700; background: #e3f2fd; padding: 10px; border-radius: 8px; margin-top: 10px;">
                                    <span style="color: #1976d2;">المبلغ الإجمالي:</span>
                                    <span style="color: #1976d2;">$${formatNumber(dollarAmount)} = ${formatNumber(iraqiAmount)} د.ع</span>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; text-align: center; background: white; padding: 15px; border-radius: 10px;">
                                <div>
                                    <div style="border-bottom: 1px solid #6c757d; height: 30px; margin-bottom: 8px; background: #f8f9fa; border-radius: 3px;"></div>
                                    <p style="font-weight: 600; color: #495057; font-size: 0.8rem;">المستلم</p>
                                </div>
                                <div>
                                    <div style="border-bottom: 1px solid #6c757d; height: 30px; margin-bottom: 8px; background: #f8f9fa; border-radius: 3px;"></div>
                                    <p style="font-weight: 600; color: #495057; font-size: 0.8rem;">المسؤول</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('receiptPreview').innerHTML = receiptHTML;
        }

        function printReceipt() {
            const receiptContent = document.getElementById('receiptPreview').innerHTML;
            const originalContent = document.body.innerHTML;

            document.body.innerHTML = receiptContent;
            window.print();
            document.body.innerHTML = originalContent;

            // Re-initialize after print
            setTimeout(() => {
                setupEventListeners();
                updatePreview();
            }, 100);
        }
    </script>
</body>
</html>