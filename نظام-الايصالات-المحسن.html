<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الإيصالات المالية - هيمن كروب</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #e1bee7 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            border: 2px solid #e3f2fd;
        }

        .header-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            margin-bottom: 20px;
        }

        .logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 4px solid #1976d2;
            margin-left: 20px;
            object-fit: cover;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .header-text h1 {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #1976d2, #7b1fa2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header-text p {
            font-size: 1.2rem;
            color: #1976d2;
            font-weight: 600;
        }

        .header-info {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            padding: 15px;
            border-radius: 15px;
            border: 2px solid #bbdefb;
            text-align: center;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            border: 2px solid #e3f2fd;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #1976d2, #7b1fa2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            text-align: center;
        }

        .card-header h2 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .card-header p {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border: 2px solid #e9ecef;
        }

        .form-section h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #495057;
            text-align: center;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
        }

        input, textarea, select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
        }

        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #1976d2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
            transform: translateY(-2px);
        }

        .radio-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 10px;
        }

        .radio-option {
            background: white;
            padding: 15px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .radio-option:hover {
            border-color: #1976d2;
            background: #f8f9fa;
        }

        .radio-option.active {
            border-color: #1976d2;
            background: #e3f2fd;
        }

        .radio-option input[type="radio"] {
            width: auto;
            margin-left: 10px;
        }

        .btn {
            background: linear-gradient(135deg, #1976d2, #1565c0);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 15px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(25, 118, 210, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            padding: 8px 15px;
            font-size: 14px;
            width: auto;
            margin: 0;
        }

        .receipt {
            background: white;
            border: 2px solid #333;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            max-height: 700px;
            overflow-y: auto;
            font-size: 14px;
        }

        .receipt-header {
            text-align: center;
            border-bottom: 3px solid #1976d2;
            padding-bottom: 20px;
            margin-bottom: 25px;
        }

        .receipt-logo-section {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
        }

        .receipt-logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 3px solid #1976d2;
            margin-left: 15px;
            object-fit: cover;
        }

        .receipt-title h1 {
            font-size: 1.8rem;
            color: #1976d2;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .receipt-title h2 {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 10px;
        }

        .receipt-type {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 10px;
            border: 2px solid #bbdefb;
        }

        .receipt-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            border: 2px solid #dee2e6;
        }

        .receipt-info h3 {
            text-align: center;
            margin-bottom: 15px;
            color: #495057;
            font-weight: 700;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
        }

        .receipt-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border-right: 4px solid #1976d2;
        }

        .amount-section {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            border: 3px solid #bbdefb;
        }

        .amount-section h3 {
            text-align: center;
            margin-bottom: 20px;
            color: #1976d2;
            font-weight: 700;
            font-size: 1.3rem;
        }

        .amount-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border-right: 4px solid #28a745;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .amount-total {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 20px;
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
        }

        .signatures {
            margin-top: 30px;
            padding-top: 25px;
            border-top: 3px solid #1976d2;
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
        }

        .signatures h4 {
            text-align: center;
            margin-bottom: 20px;
            color: #495057;
            font-weight: 700;
        }

        .signature-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .signature-box {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #dee2e6;
        }

        .signature-line {
            border-bottom: 2px solid #495057;
            height: 50px;
            margin-bottom: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .receipt-footer {
            margin-top: 25px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            background: #e3f2fd;
            padding: 20px;
            border-radius: 15px;
        }

        .duplicate {
            margin-top: 30px;
            padding-top: 25px;
            border-top: 3px dashed #6c757d;
        }

        .duplicate-header {
            text-align: center;
            background: #fff3cd;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #ffeaa7;
            margin-bottom: 20px;
        }

        .duplicate-content {
            background: #f8f9fa;
            padding: 25px;
            border: 2px solid #6c757d;
            border-radius: 15px;
            transform: scale(0.9);
        }

        .footer {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            border: 2px solid #e3f2fd;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 25px;
            margin-bottom: 25px;
        }

        .footer-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #dee2e6;
            text-align: center;
        }

        .footer-card h4 {
            margin-bottom: 10px;
            font-weight: 700;
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }

            .main-grid {
                display: none;
            }

            .header, .footer {
                display: none;
            }

            .receipt {
                border: 2px solid #000;
                page-break-inside: avoid;
                box-shadow: none;
            }
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .radio-group {
                grid-template-columns: 1fr;
            }

            .footer-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content" style="flex-direction: column; text-align: center;">
                <img src="274989432_110600838240263_3980677387415104690_n.jpg" alt="هيمن كروب" class="logo"
                     onerror="this.style.display='none'" style="margin: 0 auto 15px auto;">
                <div class="header-text">
                    <h1 style="text-align: center;">نظام إدارة الإيصالات المالية</h1>
                    <p style="text-align: center; font-size: 1.5rem; font-weight: 700; color: #1976d2;">هيمن كروب</p>
                </div>
            </div>
        </div>

        <!-- Receipts Table -->
        <div class="card" style="margin-bottom: 30px;">
            <div class="card-header">
                <h2>📊 جدول الإيصالات المحفوظة</h2>
                <p>عرض جميع الإيصالات المدخلة مع تفاصيلها</p>
            </div>

            <div style="overflow-x: auto;">
                <table id="receiptsTable" style="width: 100%; border-collapse: collapse; margin-top: 15px;">
                    <thead>
                        <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                            <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6; font-weight: 600;">رقم الإيصال</th>
                            <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6; font-weight: 600;">التاريخ</th>
                            <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6; font-weight: 600;">المشروع</th>
                            <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6; font-weight: 600;">الإجمالي ($)</th>
                            <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6; font-weight: 600;">الإجمالي (IQD)</th>
                            <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6; font-weight: 600;">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="receiptsTableBody">
                        <tr>
                            <td colspan="6" style="padding: 20px; text-align: center; color: #666; font-style: italic;">
                                لا توجد إيصالات محفوظة بعد
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-grid">
            <!-- Form Section -->
            <div class="card">
                <div class="card-header">
                    <h2>📝 إدخال بيانات الإيصال</h2>
                    <p>أدخل جميع البيانات المطلوبة لإنشاء الإيصال</p>
                </div>

                <!-- Receipt Number and Date -->
                <div class="form-section">
                    <h3>📋 معلومات الإيصال الأساسية</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="receiptNumber">🔢 رقم الإيصال</label>
                            <div style="display: flex; gap: 10px;">
                                <input type="text" id="receiptNumber" placeholder="رقم الإيصال">
                                <button type="button" class="btn-secondary" onclick="generateReceiptNumber()">🔄</button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="date">📅 التاريخ</label>
                            <input type="date" id="date">
                        </div>
                    </div>
                </div>

                <!-- Project Name -->
                <div class="form-section">
                    <div class="form-group">
                        <label for="projectName">🏗️ اسم المشروع</label>
                        <input type="text" id="projectName" placeholder="أدخل اسم المشروع (اختياري)">
                    </div>
                </div>



                <!-- Exchange Rate -->
                <div class="form-section">
                    <div class="form-group">
                        <label for="exchangeRate">📊 سعر الصرف (دينار عراقي لكل دولار أمريكي)</label>
                        <input type="number" id="exchangeRate" value="1500" min="0" step="0.01"
                               placeholder="1500" style="text-align: center; font-weight: bold;">
                        <p style="font-size: 0.8rem; color: #666; margin-top: 5px; text-align: center;">
                            💡 السعر الحالي للصرف في السوق
                        </p>
                    </div>
                </div>

                <!-- Items/Services -->
                <div class="form-section">
                    <h3>📝 تفاصيل الوصولات والخدمات</h3>
                    <div id="itemsList">
                        <div class="item-row" data-index="0">
                            <!-- Row 1: رقم الوصل وتاريخ الوصل -->
                            <div class="form-row" style="grid-template-columns: 0.8fr 2fr auto; gap: 15px; align-items: end; margin-bottom: 10px;">
                                <div class="form-group">
                                    <label style="font-size: 0.8rem; color: #666;">رقم الوصل</label>
                                    <input type="text" class="item-number" placeholder="001"
                                           style="text-align: center; font-size: 0.9rem; padding: 8px;">
                                </div>
                                <div class="form-group">
                                    <label style="font-size: 1rem; font-weight: 600;">📅 تاريخ الوصل</label>
                                    <input type="date" class="item-date"
                                           style="text-align: center; font-size: 1.1rem; padding: 12px; font-weight: 500;">
                                </div>
                                <button type="button" class="btn-secondary" onclick="removeItem(0)"
                                        style="margin-bottom: 0; height: 45px; width: 50px; font-size: 1.2rem;">❌</button>
                            </div>

                            <!-- Row 2: الوصف -->
                            <div class="form-row" style="grid-template-columns: 1fr; gap: 15px; margin-bottom: 10px;">
                                <div class="form-group">
                                    <label style="font-size: 1.1rem; font-weight: 600;">📝 وصف الخدمة أو المنتج</label>
                                    <input type="text" class="item-description" placeholder="أدخل وصف تفصيلي للخدمة أو المنتج..."
                                           style="font-size: 1.1rem; padding: 15px; font-weight: 500;">
                                </div>
                            </div>

                            <!-- Row 3: الكمية والمبلغ والعملة -->
                            <div class="form-row" style="grid-template-columns: 1fr 2fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div class="form-group">
                                    <label style="font-size: 1rem; font-weight: 600;">🔢 الكمية</label>
                                    <input type="number" class="item-quantity" value="1" min="1"
                                           style="text-align: center; font-size: 1.2rem; padding: 12px; font-weight: 600;">
                                </div>
                                <div class="form-group">
                                    <label style="font-size: 1.1rem; font-weight: 600;">💰 المبلغ</label>
                                    <input type="number" class="item-price" placeholder="0.00" min="0" step="0.01"
                                           style="text-align: center; font-size: 1.3rem; padding: 15px; font-weight: 700; color: #28a745;">
                                </div>
                                <div class="form-group">
                                    <label style="font-size: 1rem; font-weight: 600;">💱 العملة</label>
                                    <select class="item-currency"
                                            style="text-align: center; font-size: 1.1rem; padding: 12px; font-weight: 600;">
                                        <option value="USD">💵 دولار أمريكي</option>
                                        <option value="IQD">🏛️ دينار عراقي</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Separator -->
                            <div style="border-bottom: 2px dashed #dee2e6; margin: 15px 0;"></div>
                        </div>
                    </div>
                    <button type="button" class="btn-secondary" onclick="addItem()" style="width: 100%; margin-top: 10px;">➕ إضافة وصل جديد</button>
                </div>

                <!-- Total Amounts -->
                <div class="form-section">
                    <h3>💰 إجمالي المبالغ</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="totalDollar">💵 الإجمالي بالدولار ($)</label>
                            <input type="number" id="totalDollar" placeholder="0.00" readonly
                                   style="text-align: center; font-weight: bold; background: #f8f9fa;">
                        </div>

                        <div class="form-group">
                            <label for="totalIraqi">🏛️ الإجمالي بالدينار (IQD)</label>
                            <input type="number" id="totalIraqi" placeholder="0" readonly
                                   style="text-align: center; font-weight: bold; background: #f8f9fa;">
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div class="form-section">
                    <div class="form-group">
                        <label for="description">📝 وصف العملية والملاحظات</label>
                        <textarea id="description" rows="4"
                                  placeholder="أدخل وصفاً تفصيلياً للعملية أو أي ملاحظات إضافية..."></textarea>
                        <p style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                            💡 يمكنك إضافة تفاصيل إضافية حول العملية المالية
                        </p>
                    </div>
                </div>

                <!-- Save and Print Buttons -->
                <button type="button" class="btn" onclick="saveReceipt()" style="background: linear-gradient(135deg, #17a2b8, #138496); margin-bottom: 15px;">
                    💾 حفظ الإيصال في الجدول
                </button>

                <div class="form-row">
                    <button type="button" class="btn" onclick="printOriginal()" style="background: linear-gradient(135deg, #28a745, #20c997);">
                        🖨️ طباعة الأصل
                    </button>
                    <button type="button" class="btn" onclick="printArchive()" style="background: linear-gradient(135deg, #6f42c1, #e83e8c);">
                        📋 طباعة الأرشفة
                    </button>
                </div>

                <button type="button" class="btn" onclick="printBoth()" style="background: linear-gradient(135deg, #fd7e14, #dc3545); margin-top: 10px;">
                    🖨️📋 طباعة النسختين معاً
                </button>
            </div>

            <!-- Preview Section -->
            <div class="card">
                <div class="card-header" style="background: linear-gradient(135deg, #28a745, #1976d2);">
                    <h2>👁️ معاينة الإيصال</h2>
                    <p>معاينة مباشرة للإيصال قبل الطباعة</p>
                </div>

                <div id="receiptPreview">
                    <!-- Receipt content will be generated here -->
                </div>
            </div>
        </div>


    </div>

    <script>
        // Global variables
        let itemCounter = 1;
        let savedReceipts = JSON.parse(localStorage.getItem('savedReceipts')) || [];

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Set today's date
            document.getElementById('date').value = new Date().toISOString().split('T')[0];

            // Generate initial receipt number
            generateReceiptNumber();

            // Add event listeners
            setupEventListeners();

            // Load saved receipts
            loadReceiptsTable();

            // Load auto-saved data if available
            loadAutoSavedData();

            // Add initial item if none exists
            if (document.querySelectorAll('.item-row').length === 0) {
                addItem();
            } else {
                // Set today's date for existing first item
                const firstRow = document.querySelector('.item-row');
                if (firstRow) {
                    const dateInput = firstRow.querySelector('.item-date');
                    const numberInput = firstRow.querySelector('.item-number');
                    const currencyInput = firstRow.querySelector('.item-currency');
                    if (dateInput && !dateInput.value) {
                        dateInput.value = new Date().toISOString().split('T')[0];
                    }
                    if (numberInput && !numberInput.value) {
                        numberInput.value = '001';
                    }
                    if (currencyInput && !currencyInput.value) {
                        currencyInput.value = 'USD';
                    }
                }
            }

            // Initial preview update
            updatePreview();
        });

        function setupEventListeners() {
            // Add change listeners to all form inputs
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('input', handleInputChange);
                input.addEventListener('change', handleInputChange);
            });

            // Add listeners for item inputs
            setupItemListeners();
        }

        function setupItemListeners() {
            const itemInputs = document.querySelectorAll('.item-number, .item-date, .item-description, .item-quantity, .item-price, .item-currency');
            itemInputs.forEach(input => {
                input.removeEventListener('input', calculateTotals);
                input.addEventListener('input', calculateTotals);
                input.removeEventListener('change', calculateTotals);
                input.addEventListener('change', calculateTotals);
            });
        }

        function addItem() {
            const itemsList = document.getElementById('itemsList');
            const newItem = document.createElement('div');
            newItem.className = 'item-row';
            newItem.setAttribute('data-index', itemCounter);

            // Auto-generate item number and set today's date
            const itemNumber = String(itemCounter + 1).padStart(3, '0');
            const today = new Date().toISOString().split('T')[0];

            newItem.innerHTML = `
                <!-- Row 1: رقم الوصل وتاريخ الوصل -->
                <div class="form-row" style="grid-template-columns: 0.8fr 2fr auto; gap: 15px; align-items: end; margin-bottom: 10px;">
                    <div class="form-group">
                        <label style="font-size: 0.8rem; color: #666;">رقم الوصل</label>
                        <input type="text" class="item-number" value="${itemNumber}"
                               style="text-align: center; font-size: 0.9rem; padding: 8px;">
                    </div>
                    <div class="form-group">
                        <label style="font-size: 1rem; font-weight: 600;">📅 تاريخ الوصل</label>
                        <input type="date" class="item-date" value="${today}"
                               style="text-align: center; font-size: 1.1rem; padding: 12px; font-weight: 500;">
                    </div>
                    <button type="button" class="btn-secondary" onclick="removeItem(${itemCounter})"
                            style="margin-bottom: 0; height: 45px; width: 50px; font-size: 1.2rem;">❌</button>
                </div>

                <!-- Row 2: الوصف -->
                <div class="form-row" style="grid-template-columns: 1fr; gap: 15px; margin-bottom: 10px;">
                    <div class="form-group">
                        <label style="font-size: 1.1rem; font-weight: 600;">📝 وصف الخدمة أو المنتج</label>
                        <input type="text" class="item-description" placeholder="أدخل وصف تفصيلي للخدمة أو المنتج..."
                               style="font-size: 1.1rem; padding: 15px; font-weight: 500;">
                    </div>
                </div>

                <!-- Row 3: الكمية والمبلغ والعملة -->
                <div class="form-row" style="grid-template-columns: 1fr 2fr 1fr; gap: 15px; margin-bottom: 15px;">
                    <div class="form-group">
                        <label style="font-size: 1rem; font-weight: 600;">🔢 الكمية</label>
                        <input type="number" class="item-quantity" value="1" min="1"
                               style="text-align: center; font-size: 1.2rem; padding: 12px; font-weight: 600;">
                    </div>
                    <div class="form-group">
                        <label style="font-size: 1.1rem; font-weight: 600;">💰 المبلغ</label>
                        <input type="number" class="item-price" placeholder="0.00" min="0" step="0.01"
                               style="text-align: center; font-size: 1.3rem; padding: 15px; font-weight: 700; color: #28a745;">
                    </div>
                    <div class="form-group">
                        <label style="font-size: 1rem; font-weight: 600;">💱 العملة</label>
                        <select class="item-currency"
                                style="text-align: center; font-size: 1.1rem; padding: 12px; font-weight: 600;">
                            <option value="USD">💵 دولار أمريكي</option>
                            <option value="IQD">🏛️ دينار عراقي</option>
                        </select>
                    </div>
                </div>

                <!-- Separator -->
                <div style="border-bottom: 2px dashed #dee2e6; margin: 15px 0;"></div>
            `;

            itemsList.appendChild(newItem);
            itemCounter++;
            setupItemListeners();
            calculateTotals();
        }

        function removeItem(index) {
            const itemRow = document.querySelector(`[data-index="${index}"]`);
            if (itemRow && document.querySelectorAll('.item-row').length > 1) {
                itemRow.remove();
                calculateTotals();
            }
        }

        function calculateTotals() {
            let totalDollar = 0;
            let totalIraqi = 0;

            document.querySelectorAll('.item-row').forEach(row => {
                const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
                const price = parseFloat(row.querySelector('.item-price').value) || 0;
                const currency = row.querySelector('.item-currency').value;
                const itemTotal = quantity * price;

                if (currency === 'USD') {
                    totalDollar += itemTotal;
                } else if (currency === 'IQD') {
                    totalIraqi += itemTotal;
                }
            });

            const exchangeRate = parseFloat(document.getElementById('exchangeRate').value) || 1500;

            // Convert everything to both currencies for display
            const convertedIraqiFromDollar = totalDollar * exchangeRate;
            const convertedDollarFromIraqi = totalIraqi / exchangeRate;

            const finalTotalDollar = totalDollar + convertedDollarFromIraqi;
            const finalTotalIraqi = totalIraqi + convertedIraqiFromDollar;

            document.getElementById('totalDollar').value = finalTotalDollar.toFixed(2);
            document.getElementById('totalIraqi').value = Math.round(finalTotalIraqi);

            updatePreview();
        }

        function saveReceipt() {
            const receiptData = {
                receiptNumber: document.getElementById('receiptNumber').value,
                date: document.getElementById('date').value,
                projectName: document.getElementById('projectName').value,
                totalDollar: parseFloat(document.getElementById('totalDollar').value) || 0,
                totalIraqi: parseFloat(document.getElementById('totalIraqi').value) || 0,
                exchangeRate: parseFloat(document.getElementById('exchangeRate').value) || 1500,
                description: document.getElementById('description').value,
                items: [],
                timestamp: new Date().toISOString()
            };

            // Get items
            document.querySelectorAll('.item-row').forEach(row => {
                const itemNumber = row.querySelector('.item-number').value;
                const itemDate = row.querySelector('.item-date').value;
                const desc = row.querySelector('.item-description').value;
                const qty = row.querySelector('.item-quantity').value;
                const price = row.querySelector('.item-price').value;
                const currency = row.querySelector('.item-currency').value;

                if (desc || price) {
                    receiptData.items.push({
                        itemNumber: itemNumber,
                        itemDate: itemDate,
                        description: desc,
                        quantity: parseFloat(qty) || 1,
                        price: parseFloat(price) || 0,
                        currency: currency
                    });
                }
            });

            // Check if receipt already exists
            const existingIndex = savedReceipts.findIndex(r => r.receiptNumber === receiptData.receiptNumber);

            if (existingIndex >= 0) {
                if (confirm('رقم الإيصال موجود مسبقاً. هل تريد تحديثه؟')) {
                    savedReceipts[existingIndex] = receiptData;
                } else {
                    return;
                }
            } else {
                savedReceipts.push(receiptData);
            }

            // Save to localStorage
            localStorage.setItem('savedReceipts', JSON.stringify(savedReceipts));

            // Refresh table
            loadReceiptsTable();

            // Clear auto-saved draft
            localStorage.removeItem('currentFormDraft');

            // Show success message
            alert('تم حفظ الإيصال بنجاح!');

            // Clear form for new entry but keep the saved data
            clearFormForNewEntry();
        }

        function loadReceiptsTable() {
            const tbody = document.getElementById('receiptsTableBody');

            if (savedReceipts.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" style="padding: 20px; text-align: center; color: #666; font-style: italic;">
                            لا توجد إيصالات محفوظة بعد
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = savedReceipts.map((receipt, index) => {
                // Count items
                const itemsCount = receipt.items ? receipt.items.length : 0;
                const itemsText = itemsCount > 0 ? `${itemsCount} وصل` : 'لا يوجد';

                return `
                <tr style="border-bottom: 1px solid #dee2e6; ${index % 2 === 0 ? 'background: #f8f9fa;' : 'background: white;'}">
                    <td style="padding: 12px; text-align: center; border: 1px solid #dee2e6; font-family: monospace; font-weight: 600; color: #1976d2;">${receipt.receiptNumber}</td>
                    <td style="padding: 12px; text-align: center; border: 1px solid #dee2e6; font-weight: 500;">${formatDateSimple(receipt.date)}</td>
                    <td style="padding: 12px; text-align: center; border: 1px solid #dee2e6; font-weight: 500;">${receipt.projectName || '-'}</td>
                    <td style="padding: 12px; text-align: center; border: 1px solid #dee2e6; font-weight: 700; color: #28a745; font-size: 1.1rem;">$${formatNumberEn(receipt.totalDollar)}</td>
                    <td style="padding: 12px; text-align: center; border: 1px solid #dee2e6; font-weight: 700; color: #dc3545; font-size: 1.1rem;">${formatNumberEn(receipt.totalIraqi)} IQD</td>
                    <td style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">
                        <div style="display: flex; flex-direction: column; gap: 5px; align-items: center;">
                            <span style="font-size: 0.8rem; color: #666; font-weight: 500;">${itemsText}</span>
                            <div>
                                <button onclick="loadReceipt(${index})" style="background: #17a2b8; color: white; border: none; padding: 6px 12px; border-radius: 5px; margin: 2px; cursor: pointer; font-weight: 600;">📝 تحميل</button>
                                <button onclick="deleteReceipt(${index})" style="background: #dc3545; color: white; border: none; padding: 6px 12px; border-radius: 5px; margin: 2px; cursor: pointer; font-weight: 600;">🗑️ حذف</button>
                            </div>
                        </div>
                    </td>
                </tr>
                `;
            }).join('');
        }

        function loadReceipt(index) {
            const receipt = savedReceipts[index];

            document.getElementById('receiptNumber').value = receipt.receiptNumber;
            document.getElementById('date').value = receipt.date;
            document.getElementById('projectName').value = receipt.projectName || '';
            document.getElementById('exchangeRate').value = receipt.exchangeRate;
            document.getElementById('description').value = receipt.description || '';

            // Clear existing items
            document.getElementById('itemsList').innerHTML = '';
            itemCounter = 0;

            // Load items
            if (receipt.items && receipt.items.length > 0) {
                receipt.items.forEach(item => {
                    addItem();
                    const lastRow = document.querySelector(`[data-index="${itemCounter - 1}"]`);
                    lastRow.querySelector('.item-number').value = item.itemNumber || '';
                    lastRow.querySelector('.item-date').value = item.itemDate || '';
                    lastRow.querySelector('.item-description').value = item.description || '';
                    lastRow.querySelector('.item-quantity').value = item.quantity || 1;
                    lastRow.querySelector('.item-price').value = item.price || 0;
                    lastRow.querySelector('.item-currency').value = item.currency || 'USD';
                });
            } else {
                addItem();
            }

            calculateTotals();
        }

        function deleteReceipt(index) {
            if (confirm('هل أنت متأكد من حذف هذا الإيصال؟')) {
                savedReceipts.splice(index, 1);
                localStorage.setItem('savedReceipts', JSON.stringify(savedReceipts));
                loadReceiptsTable();
            }
        }

        function clearFormForNewEntry() {
            // Generate new receipt number
            generateReceiptNumber();

            // Clear project name and description
            document.getElementById('projectName').value = '';
            document.getElementById('description').value = '';

            // Reset exchange rate to default
            document.getElementById('exchangeRate').value = '1500';

            // Clear all items and add one new item
            document.getElementById('itemsList').innerHTML = '';
            itemCounter = 0;
            addItem();

            // Reset totals
            document.getElementById('totalDollar').value = '0.00';
            document.getElementById('totalIraqi').value = '0';

            // Update preview
            updatePreview();

            // Re-setup listeners
            setupItemListeners();
        }



        function handleInputChange() {
            // Recalculate totals when exchange rate changes
            calculateTotals();

            // Auto-save current form data
            autoSaveFormData();
        }

        function autoSaveFormData() {
            try {
                const currentFormData = {
                    receiptNumber: document.getElementById('receiptNumber').value,
                    date: document.getElementById('date').value,
                    projectName: document.getElementById('projectName').value,
                    exchangeRate: document.getElementById('exchangeRate').value,
                    description: document.getElementById('description').value,
                    items: [],
                    timestamp: new Date().toISOString()
                };

                // Get current items
                document.querySelectorAll('.item-row').forEach(row => {
                    const itemNumber = row.querySelector('.item-number').value;
                    const itemDate = row.querySelector('.item-date').value;
                    const desc = row.querySelector('.item-description').value;
                    const qty = row.querySelector('.item-quantity').value;
                    const price = row.querySelector('.item-price').value;
                    const currency = row.querySelector('.item-currency').value;

                    if (desc || price) {
                        currentFormData.items.push({
                            itemNumber: itemNumber,
                            itemDate: itemDate,
                            description: desc,
                            quantity: parseFloat(qty) || 1,
                            price: parseFloat(price) || 0,
                            currency: currency
                        });
                    }
                });

                // Save to localStorage as draft
                localStorage.setItem('currentFormDraft', JSON.stringify(currentFormData));
            } catch (error) {
                console.error('خطأ في الحفظ التلقائي:', error);
            }
        }

        function loadAutoSavedData() {
            try {
                const savedDraft = localStorage.getItem('currentFormDraft');
                if (savedDraft) {
                    const draftData = JSON.parse(savedDraft);

                    // Only load if it's recent (within last hour)
                    const draftTime = new Date(draftData.timestamp);
                    const now = new Date();
                    const hoursDiff = (now - draftTime) / (1000 * 60 * 60);

                    if (hoursDiff < 1 && confirm('تم العثور على بيانات محفوظة مؤقتاً. هل تريد استعادتها؟')) {
                        // Load the draft data
                        document.getElementById('receiptNumber').value = draftData.receiptNumber || '';
                        document.getElementById('date').value = draftData.date || '';
                        document.getElementById('projectName').value = draftData.projectName || '';
                        document.getElementById('exchangeRate').value = draftData.exchangeRate || '1500';
                        document.getElementById('description').value = draftData.description || '';

                        // Load items
                        if (draftData.items && draftData.items.length > 0) {
                            document.getElementById('itemsList').innerHTML = '';
                            itemCounter = 0;

                            draftData.items.forEach(item => {
                                addItem();
                                const lastRow = document.querySelector(`[data-index="${itemCounter - 1}"]`);
                                lastRow.querySelector('.item-number').value = item.itemNumber || '';
                                lastRow.querySelector('.item-date').value = item.itemDate || '';
                                lastRow.querySelector('.item-description').value = item.description || '';
                                lastRow.querySelector('.item-quantity').value = item.quantity || 1;
                                lastRow.querySelector('.item-price').value = item.price || 0;
                                lastRow.querySelector('.item-currency').value = item.currency || 'USD';
                            });
                        }

                        calculateTotals();
                    }
                }
            } catch (error) {
                console.error('خطأ في تحميل البيانات المحفوظة:', error);
            }
        }

        function generateReceiptNumber() {
            const now = new Date();
            const year = now.getFullYear();
            const month = (now.getMonth() + 1).toString().padStart(2, '0');
            const day = now.getDate().toString().padStart(2, '0');
            const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

            const receiptNumber = `${year}${month}${day}-${random}`;
            document.getElementById('receiptNumber').value = receiptNumber;
            updatePreview();
        }

        function formatNumber(num) {
            if (isNaN(num) || num === null || num === undefined) return '0';
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 2
            }).format(Number(num));
        }

        function formatNumberEn(num) {
            if (isNaN(num) || num === null || num === undefined) return '0';
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 2
            }).format(Number(num));
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        function formatDateSimple(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        }

        function updatePreview() {
            const receiptNumber = document.getElementById('receiptNumber').value || '---';
            const date = document.getElementById('date').value;
            const projectName = document.getElementById('projectName').value;
            const totalDollar = parseFloat(document.getElementById('totalDollar').value) || 0;
            const totalIraqi = parseFloat(document.getElementById('totalIraqi').value) || 0;
            const exchangeRate = parseFloat(document.getElementById('exchangeRate').value) || 1500;
            const description = document.getElementById('description').value;

            // Get items
            let itemsHTML = '';
            document.querySelectorAll('.item-row').forEach((row, index) => {
                const itemNumber = row.querySelector('.item-number').value;
                const itemDate = row.querySelector('.item-date').value;
                const desc = row.querySelector('.item-description').value;
                const qty = row.querySelector('.item-quantity').value;
                const price = row.querySelector('.item-price').value;
                const currency = row.querySelector('.item-currency').value;
                const total = (parseFloat(qty) || 0) * (parseFloat(price) || 0);

                let currencyDisplay = '';
                let currencyColor = '#28a745';

                if (currency === 'USD') {
                    currencyDisplay = `$${formatNumberEn(total.toFixed(2))}`;
                    currencyColor = '#28a745';
                } else if (currency === 'IQD') {
                    currencyDisplay = `${formatNumberEn(Math.round(total))} د.ع`;
                    currencyColor = '#dc3545';
                }

                if (desc || price) {
                    itemsHTML += `
                        <div style="display: flex; justify-content: space-between; padding: 8px; background: white; border-radius: 5px; margin-bottom: 4px; border-right: 3px solid #1976d2; font-size: 0.9rem;">
                            <span style="flex: 0.8; text-align: center; font-weight: 600; color: #1976d2; font-size: 0.8rem;">${itemNumber || (index + 1).toString().padStart(3, '0')}</span>
                            <span style="flex: 1.2; text-align: center; font-size: 0.8rem; color: #666;">${formatDateSimple(itemDate) || ''}</span>
                            <span style="flex: 3; font-weight: 500; padding-right: 8px;">${desc || 'خدمة ' + (index + 1)}</span>
                            <span style="flex: 0.8; text-align: center; font-weight: 600;">${formatNumberEn(qty || 1)}</span>
                            <span style="flex: 1.5; text-align: center; font-weight: 700; color: ${currencyColor}; font-size: 1rem;">${currencyDisplay}</span>
                        </div>
                    `;
                }
            });

            const receiptHTML = `
                <div class="receipt">
                    <div style="text-align: center; border-bottom: 2px solid #1976d2; padding-bottom: 15px; margin-bottom: 15px;">
                        <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
                            <img src="274989432_110600838240263_3980677387415104690_n.jpg" alt="هيمن كروب"
                                 style="width: 50px; height: 50px; border-radius: 50%; border: 2px solid #1976d2; margin-left: 10px; object-fit: cover;"
                                 onerror="this.style.display='none'">
                            <div>
                                <h1 style="color: #1976d2; font-size: 1.4rem; margin: 0;">هيمن كروب</h1>
                                <h2 style="color: #666; font-size: 1rem; margin: 0;">HEMEN GROUP</h2>
                            </div>
                        </div>
                        <p style="color: #1976d2; font-weight: 600; font-size: 0.9rem; margin: 0;">💰 إيصال صرف مالي</p>
                    </div>

                    <div style="background: #f8f9fa; padding: 12px; border-radius: 8px; margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span style="font-weight: 600;">رقم الإيصال:</span>
                            <span style="font-family: monospace; background: #e3f2fd; padding: 3px 8px; border-radius: 3px;">${receiptNumber}</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span style="font-weight: 600;">التاريخ:</span>
                            <span>${formatDate(date)}</span>
                        </div>
                        ${projectName ? `
                        <div style="display: flex; justify-content: space-between;">
                            <span style="font-weight: 600;">المشروع:</span>
                            <span>${projectName}</span>
                        </div>
                        ` : ''}
                    </div>

                    ${itemsHTML ? `
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 2px solid #dee2e6;">
                        <h3 style="margin: 0 0 12px 0; color: #495057; font-size: 1.1rem; text-align: center; font-weight: 700;">📋 جدول تفاصيل الوصولات والخدمات</h3>
                        <div style="display: flex; justify-content: space-between; font-weight: 700; background: #495057; color: white; padding: 8px; border-radius: 5px; margin-bottom: 10px; font-size: 0.8rem;">
                            <span style="flex: 0.8; text-align: center;">رقم</span>
                            <span style="flex: 1.2; text-align: center;">التاريخ</span>
                            <span style="flex: 3; text-align: center;">الوصف</span>
                            <span style="flex: 0.8; text-align: center;">الكمية</span>
                            <span style="flex: 1.5; text-align: center;">المبلغ</span>
                        </div>
                        ${itemsHTML}
                    </div>
                    ` : ''}

                    <div style="background: linear-gradient(135deg, #e3f2fd, #f3e5f5); padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 2px solid #bbdefb;">
                        <h3 style="margin: 0 0 12px 0; text-align: center; color: #1976d2; font-size: 1rem;">💰 إجمالي المبالغ</h3>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px; padding: 8px; background: white; border-radius: 5px;">
                            <span style="font-weight: 600;">💵 الإجمالي بالدولار:</span>
                            <span style="color: #28a745; font-weight: 700;">$${formatNumberEn(totalDollar)}</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px; padding: 8px; background: white; border-radius: 5px;">
                            <span style="font-weight: 600;">📊 سعر الصرف:</span>
                            <span style="color: #1976d2; font-weight: 700;">${formatNumberEn(exchangeRate)} IQD</span>
                        </div>
                        <div style="background: linear-gradient(135deg, #dc3545, #c82333); color: white; padding: 12px; border-radius: 8px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="font-weight: 700;">🏛️ الإجمالي بالدينار:</span>
                                <span style="font-weight: 700; font-size: 1.1rem;">${formatNumberEn(totalIraqi)} IQD</span>
                            </div>
                        </div>
                    </div>

                    ${description ? `
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 8px; margin-bottom: 15px;">
                        <h4 style="margin: 0 0 8px 0; color: #495057; font-size: 0.9rem;">📝 وصف العملية:</h4>
                        <p style="background: white; padding: 10px; border-radius: 5px; color: #666; margin: 0; font-size: 0.8rem;">${description}</p>
                    </div>
                    ` : ''}

                    <div style="margin-top: 20px; padding-top: 15px; border-top: 2px solid #1976d2; background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <h4 style="text-align: center; margin: 0 0 15px 0; color: #495057; font-size: 0.9rem;">✍️ التوقيعات</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div style="text-align: center; background: white; padding: 12px; border-radius: 8px;">
                                <div style="border-bottom: 2px solid #495057; height: 30px; margin-bottom: 8px; background: #f8f9fa; border-radius: 3px;"></div>
                                <p style="font-weight: 600; color: #495057; font-size: 0.8rem; margin: 0;">👤 المستلم</p>
                            </div>
                            <div style="text-align: center; background: white; padding: 12px; border-radius: 8px;">
                                <div style="border-bottom: 2px solid #495057; height: 30px; margin-bottom: 8px; background: #f8f9fa; border-radius: 3px;"></div>
                                <p style="font-weight: 600; color: #495057; font-size: 0.8rem; margin: 0;">👨‍💼 المسؤول</p>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 15px; text-align: center; background: #e3f2fd; padding: 10px; border-radius: 8px;">
                        <p style="color: #1976d2; font-weight: 600; font-size: 0.8rem; margin: 0;">✅ إيصال صالح للأرشفة والمراجعة</p>
                        <p style="color: #999; font-size: 0.7rem; margin: 5px 0 0 0;">هيمن كروب - نظام الإيصالات المالية</p>
                    </div>

                    <div id="archiveSection" style="margin-top: 20px; padding-top: 15px; border-top: 2px dashed #6c757d;">
                        <div style="text-align: center; background: #fff3cd; padding: 8px; border-radius: 5px; border: 1px solid #ffeaa7; margin-bottom: 15px;">
                            <p style="color: #856404; font-weight: 600; font-size: 0.8rem; margin: 0;">📋 نسخة الأرشفة</p>
                        </div>

                        <div style="background: #f8f9fa; padding: 15px; border: 1px solid #6c757d; border-radius: 8px; transform: scale(0.95);">
                            <div style="text-align: center; margin-bottom: 12px;">
                                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 8px;">
                                    <img src="274989432_110600838240263_3980677387415104690_n.jpg" alt="هيمن كروب"
                                         style="width: 30px; height: 30px; border-radius: 50%; border: 1px solid #6c757d; margin-left: 8px; object-fit: cover;"
                                         onerror="this.style.display='none'">
                                    <div>
                                        <h3 style="color: #495057; font-size: 0.9rem; margin: 0;">هيمن كروب</h3>
                                        <p style="color: #6c757d; font-size: 0.7rem; margin: 0;">نسخة أرشفة</p>
                                    </div>
                                </div>
                            </div>

                            <div style="background: white; padding: 10px; border-radius: 5px; margin-bottom: 10px; font-size: 0.7rem;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <span>رقم الإيصال:</span>
                                    <span style="font-family: monospace;">${receiptNumber}</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <span>التاريخ:</span>
                                    <span>${formatDate(date)}</span>
                                </div>
                                ${projectName ? `
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <span>المشروع:</span>
                                    <span>${projectName}</span>
                                </div>
                                ` : ''}
                                <div style="display: flex; justify-content: space-between; font-weight: 600; background: #e3f2fd; padding: 5px; border-radius: 3px; margin-top: 8px;">
                                    <span>الإجمالي:</span>
                                    <span>$${formatNumberEn(totalDollar)} = ${formatNumberEn(totalIraqi)} IQD</span>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; text-align: center; font-size: 0.7rem;">
                                <div style="background: white; padding: 8px; border-radius: 3px;">
                                    <div style="border-bottom: 1px solid #6c757d; height: 20px; margin-bottom: 5px;"></div>
                                    <p style="margin: 0;">المستلم</p>
                                </div>
                                <div style="background: white; padding: 8px; border-radius: 3px;">
                                    <div style="border-bottom: 1px solid #6c757d; height: 20px; margin-bottom: 5px;"></div>
                                    <p style="margin: 0;">المسؤول</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('receiptPreview').innerHTML = receiptHTML;
        }

        function printOriginal() {
            if (!confirm('هل تريد طباعة النسخة الأصلية للإيصال؟')) {
                return;
            }

            try {
                const receiptContent = document.getElementById('receiptPreview').innerHTML;
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = receiptContent;

                // Remove archive section
                const archiveSection = tempDiv.querySelector('#archiveSection');
                if (archiveSection) {
                    archiveSection.remove();
                }

                // Create print window
                const printWindow = window.open('', '_blank');
                if (printWindow) {
                    printWindow.document.write(`
                        <html>
                        <head>
                            <title>Receipt</title>
                            <style>
                                body { font-family: 'Arial', sans-serif; margin: 20px; direction: rtl; }
                                .receipt { max-width: 800px; margin: 0 auto; }
                                @media print { body { margin: 0; } }
                            </style>
                        </head>
                        <body>
                            ${tempDiv.innerHTML}
                        </body>
                        </html>
                    `);
                    printWindow.document.close();

                    // Handle print completion
                    printWindow.onafterprint = function() {
                        printWindow.close();
                    };

                    printWindow.print();
                } else {
                    alert('تعذر فتح نافذة الطباعة. يرجى التأكد من السماح بالنوافذ المنبثقة.');
                }
            } catch (error) {
                console.error('خطأ في الطباعة:', error);
                alert('حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.');
            }
        }

        function printArchive() {
            if (!confirm('هل تريد طباعة نسخة الأرشفة؟')) {
                return;
            }

            try {
                const receiptContent = document.getElementById('receiptPreview').innerHTML;
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = receiptContent;

                // Keep only archive section
                const archiveSection = tempDiv.querySelector('#archiveSection');
                if (archiveSection) {
                    tempDiv.innerHTML = '<div class="receipt">' + archiveSection.outerHTML + '</div>';
                }

                // Create print window
                const printWindow = window.open('', '_blank');
                if (printWindow) {
                    printWindow.document.write(`
                        <html>
                        <head>
                            <title>Archive</title>
                            <style>
                                body { font-family: 'Arial', sans-serif; margin: 20px; direction: rtl; }
                                .receipt { max-width: 600px; margin: 0 auto; }
                                @media print { body { margin: 0; } }
                            </style>
                        </head>
                        <body>
                            ${tempDiv.innerHTML}
                        </body>
                        </html>
                    `);
                    printWindow.document.close();

                    // Handle print completion
                    printWindow.onafterprint = function() {
                        printWindow.close();
                    };

                    printWindow.print();
                } else {
                    alert('تعذر فتح نافذة الطباعة. يرجى التأكد من السماح بالنوافذ المنبثقة.');
                }
            } catch (error) {
                console.error('خطأ في الطباعة:', error);
                alert('حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.');
            }
        }

        function printBoth() {
            if (!confirm('هل تريد طباعة النسختين معاً (الأصل + الأرشفة)؟')) {
                return;
            }

            try {
                const receiptContent = document.getElementById('receiptPreview').innerHTML;

                // Create print window
                const printWindow = window.open('', '_blank');
                if (printWindow) {
                    printWindow.document.write(`
                        <html>
                        <head>
                            <title>Complete Receipt</title>
                            <style>
                                body { font-family: 'Arial', sans-serif; margin: 20px; direction: rtl; }
                                .receipt { max-width: 800px; margin: 0 auto; page-break-inside: avoid; }
                                @media print {
                                    body { margin: 0; }
                                    #archiveSection { page-break-before: always; }
                                }
                            </style>
                        </head>
                        <body>
                            ${receiptContent}
                        </body>
                        </html>
                    `);
                    printWindow.document.close();

                    // Handle print completion
                    printWindow.onafterprint = function() {
                        printWindow.close();
                    };

                    printWindow.print();
                } else {
                    alert('تعذر فتح نافذة الطباعة. يرجى التأكد من السماح بالنوافذ المنبثقة.');
                }
            } catch (error) {
                console.error('خطأ في الطباعة:', error);
                alert('حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.');
            }
        }
    </script>
</body>
</html>