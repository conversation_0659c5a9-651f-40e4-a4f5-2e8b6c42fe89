import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // تحسين الأداء والتحميل
  experimental: {
    optimizeCss: true,
  },

  // ضغط الصور
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // تحسين الخطوط
  optimizeFonts: true,

  // ضغط الملفات
  compress: true,

  // تحسين البناء
  swcMinify: true,

  // إعدادات الأمان
  poweredByHeader: false,

  // دعم RTL
  i18n: {
    locales: ['ar', 'en'],
    defaultLocale: 'ar',
  },
};

export default nextConfig;
