'use client';

import { useState, useRef } from 'react';
import { ReceiptForm } from '@/components/ReceiptForm';
import { ReceiptPreview } from '@/components/ReceiptPreview';

export interface ReceiptData {
  receiptNumber: string;
  date: string;
  dollarAmount: number;
  iraqiAmount: number;
  exchangeRate: number;
  description: string;
  projectName: string;
}

export default function Home() {
  const [receiptData, setReceiptData] = useState<ReceiptData>({
    receiptNumber: '',
    date: new Date().toISOString().split('T')[0],
    dollarAmount: 0,
    iraqiAmount: 0,
    exchangeRate: 1500,
    description: '',
    projectName: '',
  });

  const printRef = useRef<HTMLDivElement>(null);

  const handlePrint = () => {
    if (printRef.current) {
      const printContent = printRef.current.innerHTML;
      const originalContent = document.body.innerHTML;

      document.body.innerHTML = printContent;
      window.print();
      document.body.innerHTML = originalContent;
      window.location.reload();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-4">
      <div className="max-w-7xl mx-auto">
        <header className="text-center mb-8 bg-white rounded-2xl shadow-lg p-6 border border-blue-200">
          <div className="flex items-center justify-center mb-4">
            <img
              src="/logo.jpg"
              alt="هيمن كروب"
              className="w-20 h-20 rounded-full object-cover border-4 border-blue-600 shadow-lg mr-4"
              onError={(e) => {
                e.currentTarget.style.display = 'none';
              }}
            />
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-800 to-purple-800 bg-clip-text text-transparent mb-2">
                نظام إدارة الإيصالات المالية
              </h1>
              <p className="text-xl text-blue-700 font-semibold">
                هيمن كروب - نظام صرف الفواتير الاحترافي
              </p>
            </div>
          </div>
          <div className="bg-gradient-to-r from-blue-100 to-purple-100 px-6 py-3 rounded-lg border border-blue-300">
            <p className="text-blue-800 font-medium">
              💼 نظام متطور لإدارة الإيصالات المالية وتحويل العملات
            </p>
            <p className="text-sm text-blue-600 mt-1">
              Professional Financial Receipt Management System
            </p>
          </div>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="bg-white rounded-2xl shadow-xl p-6 border border-blue-200 hover:shadow-2xl transition-shadow duration-300">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-xl mb-6">
              <h2 className="text-2xl font-bold text-center flex items-center justify-center gap-3">
                📝 إدخال بيانات الإيصال
                <span className="text-sm bg-white bg-opacity-20 px-2 py-1 rounded-full">Input</span>
              </h2>
              <p className="text-center text-blue-100 text-sm mt-2">
                أدخل جميع البيانات المطلوبة لإنشاء الإيصال
              </p>
            </div>
            <ReceiptForm
              receiptData={receiptData}
              setReceiptData={setReceiptData}
              onPrint={handlePrint}
            />
          </div>

          <div className="bg-white rounded-2xl shadow-xl p-6 border border-green-200 hover:shadow-2xl transition-shadow duration-300">
            <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white p-4 rounded-xl mb-6">
              <h2 className="text-2xl font-bold text-center flex items-center justify-center gap-3">
                👁️ معاينة الإيصال
                <span className="text-sm bg-white bg-opacity-20 px-2 py-1 rounded-full">Preview</span>
              </h2>
              <p className="text-center text-green-100 text-sm mt-2">
                معاينة مباشرة للإيصال قبل الطباعة
              </p>
            </div>
            <div ref={printRef} className="max-h-[800px] overflow-y-auto">
              <ReceiptPreview receiptData={receiptData} />
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="mt-12 bg-white rounded-2xl shadow-lg p-6 border border-gray-200">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <img
                src="/logo.jpg"
                alt="هيمن كروب"
                className="w-12 h-12 rounded-full object-cover border-2 border-blue-600 mr-3"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
              <div>
                <h3 className="text-xl font-bold text-blue-800">هيمن كروب</h3>
                <p className="text-sm text-blue-600">HEMEN GROUP</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 className="font-semibold text-blue-800 mb-2">🏢 معلومات الشركة</h4>
                <p className="text-sm text-blue-700">شركة هيمن كروب للخدمات المالية</p>
                <p className="text-xs text-blue-600">نظام إدارة الإيصالات الاحترافي</p>
              </div>

              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 className="font-semibold text-green-800 mb-2">⚡ الميزات</h4>
                <p className="text-sm text-green-700">حساب تلقائي للعملات</p>
                <p className="text-xs text-green-600">طباعة احترافية مع نسخة أرشفة</p>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <h4 className="font-semibold text-purple-800 mb-2">🔒 الأمان</h4>
                <p className="text-sm text-purple-700">نظام آمن ومحمي</p>
                <p className="text-xs text-purple-600">حفظ تلقائي للبيانات</p>
              </div>
            </div>

            <div className="border-t border-gray-200 pt-4">
              <p className="text-sm text-gray-600">
                © 2024 هيمن كروب - جميع الحقوق محفوظة | HEMEN GROUP - All Rights Reserved
              </p>
              <p className="text-xs text-gray-500 mt-1">
                نظام إدارة الإيصالات المالية الاحترافي v2.0
              </p>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
}
