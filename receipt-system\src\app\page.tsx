'use client';

import { useState, useRef } from 'react';
import { ReceiptForm } from '@/components/ReceiptForm';
import { ReceiptPreview } from '@/components/ReceiptPreview';

export interface ReceiptData {
  receiptNumber: string;
  date: string;
  dollarAmount: number;
  iraqiAmount: number;
  exchangeRate: number;
  description: string;
  projectName: string;
}

export default function Home() {
  const [receiptData, setReceiptData] = useState<ReceiptData>({
    receiptNumber: '',
    date: new Date().toISOString().split('T')[0],
    dollarAmount: 0,
    iraqiAmount: 0,
    exchangeRate: 1500,
    description: '',
    projectName: '',
  });

  const printRef = useRef<HTMLDivElement>(null);

  const handlePrint = () => {
    if (printRef.current) {
      const printContent = printRef.current.innerHTML;
      const originalContent = document.body.innerHTML;

      document.body.innerHTML = printContent;
      window.print();
      document.body.innerHTML = originalContent;
      window.location.reload();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-7xl mx-auto">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            نظام إدارة الإيصالات
          </h1>
          <p className="text-lg text-gray-600">
            هيمن كروب - نظام صرف الفواتير
          </p>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
              إدخال بيانات الإيصال
            </h2>
            <ReceiptForm
              receiptData={receiptData}
              setReceiptData={setReceiptData}
              onPrint={handlePrint}
            />
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
              معاينة الإيصال
            </h2>
            <div ref={printRef}>
              <ReceiptPreview receiptData={receiptData} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
