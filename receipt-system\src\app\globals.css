@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Arabic font support */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Noto Sans Arabic', Arial, Helvetica, sans-serif;
  direction: rtl;
}

/* Print styles */
@media print {
  body {
    background: white !important;
    color: black !important;
  }

  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  /* Ensure proper page breaks */
  .receipt-container {
    page-break-inside: avoid;
    margin: 0;
    padding: 20px;
  }

  /* Hide unnecessary elements during print */
  header, nav, footer, .sidebar {
    display: none !important;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Enhanced animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Professional animations */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-pulse-gentle {
  animation: pulse 2s infinite;
}

/* Enhanced form inputs */
input:focus, textarea:focus, select:focus {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

/* Professional button effects */
button {
  transition: all 0.3s ease;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

button:active {
  transform: translateY(0);
}

/* Receipt specific styles */
.receipt-container {
  font-family: 'Noto Sans Arabic', 'Arial', sans-serif;
  line-height: 1.6;
}

.receipt-container img {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Professional gradients */
.gradient-blue {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-green {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-purple {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Enhanced shadows */
.shadow-professional {
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.shadow-receipt {
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}
