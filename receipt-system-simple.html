<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الإيصالات - هيمن كروب</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #1976d2;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2rem;
            color: #666;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .card h2 {
            text-align: center;
            margin-bottom: 25px;
            color: #333;
            font-size: 1.5rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #1976d2;
        }

        .radio-group {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }

        .radio-group label {
            display: flex;
            align-items: center;
            margin-bottom: 0;
            cursor: pointer;
        }

        .radio-group input[type="radio"] {
            width: auto;
            margin-left: 8px;
        }

        .btn {
            background: #1976d2;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
            width: 100%;
        }

        .btn:hover {
            background: #1565c0;
        }

        .btn-secondary {
            background: #666;
            margin-right: 10px;
            width: auto;
            padding: 8px 15px;
            font-size: 14px;
        }

        .btn-secondary:hover {
            background: #555;
        }

        .receipt {
            background: white;
            border: 2px solid #333;
            padding: 30px;
            max-width: 400px;
            margin: 0 auto;
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
        }

        .receipt-header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .receipt-header h1 {
            font-size: 1.8rem;
            margin-bottom: 5px;
            color: #333;
        }

        .receipt-header h2 {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 10px;
        }

        .receipt-header p {
            font-size: 0.9rem;
            color: #888;
        }

        .receipt-info {
            margin-bottom: 20px;
        }

        .receipt-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }

        .receipt-row.highlight {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            font-weight: 600;
        }

        .amount-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .amount-section h3 {
            text-align: center;
            margin-bottom: 15px;
            color: #333;
        }

        .signatures {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #333;
        }

        .signature-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            text-align: center;
        }

        .signature-box {
            border-bottom: 1px solid #333;
            height: 40px;
            margin-bottom: 10px;
        }

        .receipt-footer {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #ccc;
            text-align: center;
            font-size: 0.8rem;
            color: #666;
        }

        .duplicate {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px dashed #666;
        }

        .duplicate .receipt {
            background: #f8f9fa;
            border: 1px solid #666;
            padding: 20px;
            transform: scale(0.8);
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .main-content {
                display: none;
            }
            
            .header {
                display: none;
            }
            
            .receipt {
                border: 2px solid #000;
                page-break-inside: avoid;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>نظام إدارة الإيصالات</h1>
            <p>هيمن كروب - نظام صرف الفواتير</p>
        </div>

        <div class="main-content">
            <!-- Form Section -->
            <div class="card">
                <h2>إدخال بيانات الإيصال</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="receiptNumber">رقم الإيصال</label>
                        <div style="display: flex; gap: 10px;">
                            <input type="text" id="receiptNumber" placeholder="رقم الإيصال">
                            <button type="button" class="btn-secondary" onclick="generateReceiptNumber()">🔄</button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="date">التاريخ</label>
                        <input type="date" id="date">
                    </div>
                </div>

                <div class="form-group">
                    <label for="projectName">اسم المشروع</label>
                    <input type="text" id="projectName" placeholder="اسم المشروع">
                </div>

                <div class="form-group">
                    <label>طريقة الحساب</label>
                    <div class="radio-group">
                        <label>
                            <input type="radio" name="calculationMode" value="dollar-to-iraqi" checked>
                            من الدولار إلى الدينار
                        </label>
                        <label>
                            <input type="radio" name="calculationMode" value="iraqi-to-dollar">
                            من الدينار إلى الدولار
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label for="exchangeRate">سعر الصرف (دينار لكل دولار)</label>
                    <input type="number" id="exchangeRate" value="1500" min="0" step="0.01">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="dollarAmount">المبلغ بالدولار ($)</label>
                        <input type="number" id="dollarAmount" placeholder="0.00" min="0" step="0.01">
                    </div>
                    
                    <div class="form-group">
                        <label for="iraqiAmount">المبلغ بالدينار العراقي (IQD)</label>
                        <input type="number" id="iraqiAmount" placeholder="0" min="0" step="1">
                    </div>
                </div>

                <div class="form-group">
                    <label for="description">وصف العملية</label>
                    <textarea id="description" rows="3" placeholder="وصف تفصيلي للعملية..."></textarea>
                </div>

                <button type="button" class="btn" onclick="printReceipt()">🖨️ طباعة الإيصال</button>
            </div>

            <!-- Preview Section -->
            <div class="card">
                <h2>معاينة الإيصال</h2>
                <div id="receiptPreview">
                    <!-- Receipt content will be generated here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Set today's date
            document.getElementById('date').value = new Date().toISOString().split('T')[0];
            
            // Generate initial receipt number
            generateReceiptNumber();
            
            // Add event listeners
            setupEventListeners();
            
            // Initial preview update
            updatePreview();
        });

        function setupEventListeners() {
            // Add change listeners to all form inputs
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('input', handleInputChange);
                input.addEventListener('change', handleInputChange);
            });
        }

        function handleInputChange(event) {
            const field = event.target.id;
            const value = event.target.value;
            
            // Auto-calculate based on mode
            const mode = document.querySelector('input[name="calculationMode"]:checked').value;
            const exchangeRate = parseFloat(document.getElementById('exchangeRate').value) || 1500;
            
            if (field === 'dollarAmount' && mode === 'dollar-to-iraqi') {
                const dollarAmount = parseFloat(value) || 0;
                document.getElementById('iraqiAmount').value = Math.round(dollarAmount * exchangeRate);
            } else if (field === 'iraqiAmount' && mode === 'iraqi-to-dollar') {
                const iraqiAmount = parseFloat(value) || 0;
                document.getElementById('dollarAmount').value = (iraqiAmount / exchangeRate).toFixed(2);
            } else if (field === 'exchangeRate') {
                if (mode === 'dollar-to-iraqi') {
                    const dollarAmount = parseFloat(document.getElementById('dollarAmount').value) || 0;
                    document.getElementById('iraqiAmount').value = Math.round(dollarAmount * exchangeRate);
                } else {
                    const iraqiAmount = parseFloat(document.getElementById('iraqiAmount').value) || 0;
                    document.getElementById('dollarAmount').value = (iraqiAmount / exchangeRate).toFixed(2);
                }
            }
            
            // Update preview
            updatePreview();
        }

        function generateReceiptNumber() {
            const now = new Date();
            const year = now.getFullYear();
            const month = (now.getMonth() + 1).toString().padStart(2, '0');
            const day = now.getDate().toString().padStart(2, '0');
            const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            
            const receiptNumber = `${year}${month}${day}-${random}`;
            document.getElementById('receiptNumber').value = receiptNumber;
            updatePreview();
        }

        function formatNumber(num) {
            return new Intl.NumberFormat('ar-IQ').format(num);
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-IQ', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        function updatePreview() {
            const receiptNumber = document.getElementById('receiptNumber').value || '---';
            const date = document.getElementById('date').value;
            const projectName = document.getElementById('projectName').value;
            const dollarAmount = parseFloat(document.getElementById('dollarAmount').value) || 0;
            const iraqiAmount = parseFloat(document.getElementById('iraqiAmount').value) || 0;
            const exchangeRate = parseFloat(document.getElementById('exchangeRate').value) || 1500;
            const description = document.getElementById('description').value;

            const receiptHTML = `
                <div class="receipt">
                    <div class="receipt-header">
                        <h1>هيمن كروب</h1>
                        <h2>HEMEN GROUP</h2>
                        <p>إيصال صرف</p>
                    </div>

                    <div class="receipt-info">
                        <div class="receipt-row">
                            <span><strong>رقم الإيصال:</strong></span>
                            <span>${receiptNumber}</span>
                        </div>
                        <div class="receipt-row">
                            <span><strong>التاريخ:</strong></span>
                            <span>${formatDate(date)}</span>
                        </div>
                        ${projectName ? `
                        <div class="receipt-row">
                            <span><strong>المشروع:</strong></span>
                            <span>${projectName}</span>
                        </div>
                        ` : ''}
                    </div>

                    <div class="amount-section">
                        <h3>تفاصيل المبلغ</h3>
                        <div class="receipt-row">
                            <span>المبلغ بالدولار:</span>
                            <span style="color: #2e7d32; font-weight: bold;">$${formatNumber(dollarAmount)}</span>
                        </div>
                        <div class="receipt-row">
                            <span>سعر الصرف:</span>
                            <span style="color: #1976d2; font-weight: bold;">${formatNumber(exchangeRate)} د.ع</span>
                        </div>
                        <div class="receipt-row highlight">
                            <span>المبلغ بالدينار:</span>
                            <span style="color: #d32f2f; font-weight: bold;">${formatNumber(iraqiAmount)} د.ع</span>
                        </div>
                    </div>

                    ${description ? `
                    <div style="margin: 20px 0;">
                        <h4 style="margin-bottom: 10px; color: #555;"><strong>الوصف:</strong></h4>
                        <p style="background: #f5f5f5; padding: 10px; border-radius: 5px; font-size: 14px; color: #666;">${description}</p>
                    </div>
                    ` : ''}

                    <div class="signatures">
                        <div class="signature-row">
                            <div>
                                <div class="signature-box"></div>
                                <p style="font-size: 14px; color: #666;">المستلم</p>
                                <p style="font-size: 12px; color: #999;">Receiver</p>
                            </div>
                            <div>
                                <div class="signature-box"></div>
                                <p style="font-size: 14px; color: #666;">المسؤول</p>
                                <p style="font-size: 12px; color: #999;">Authorized</p>
                            </div>
                        </div>
                    </div>

                    <div class="receipt-footer">
                        <p>هذا الإيصال صالح للأرشفة والمراجعة</p>
                        <p style="margin-top: 5px;">This receipt is valid for archiving and review</p>
                    </div>

                    <!-- Duplicate Copy -->
                    <div class="duplicate">
                        <div style="text-align: center; margin-bottom: 15px; font-size: 14px; color: #666;">
                            <p>نسخة للأرشفة - Archive Copy</p>
                        </div>
                        
                        <div style="background: #f8f9fa; padding: 20px; border: 1px solid #666; border-radius: 8px;">
                            <div style="text-align: center; margin-bottom: 15px;">
                                <h3 style="margin-bottom: 5px; color: #555;">هيمن كروب</h3>
                                <h4 style="color: #777; margin-bottom: 5px;">HEMEN GROUP</h4>
                                <p style="font-size: 12px; color: #888;">إيصال صرف - نسخة الأرشفة</p>
                            </div>

                            <div style="font-size: 14px; margin-bottom: 15px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <span>رقم الإيصال:</span>
                                    <span>${receiptNumber}</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <span>التاريخ:</span>
                                    <span>${formatDate(date)}</span>
                                </div>
                                ${projectName ? `
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <span>المشروع:</span>
                                    <span>${projectName}</span>
                                </div>
                                ` : ''}
                                <div style="display: flex; justify-content: space-between; font-weight: bold; padding-top: 10px; border-top: 1px solid #ccc;">
                                    <span>المبلغ:</span>
                                    <span>$${formatNumber(dollarAmount)} = ${formatNumber(iraqiAmount)} د.ع</span>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; text-align: center; font-size: 12px; padding-top: 15px; border-top: 1px solid #ccc;">
                                <div>
                                    <div style="border-bottom: 1px solid #666; height: 25px; margin-bottom: 5px;"></div>
                                    <p>المستلم</p>
                                </div>
                                <div>
                                    <div style="border-bottom: 1px solid #666; height: 25px; margin-bottom: 5px;"></div>
                                    <p>المسؤول</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('receiptPreview').innerHTML = receiptHTML;
        }

        function printReceipt() {
            const receiptContent = document.getElementById('receiptPreview').innerHTML;
            const originalContent = document.body.innerHTML;
            
            document.body.innerHTML = receiptContent;
            window.print();
            document.body.innerHTML = originalContent;
            
            // Re-initialize after print
            setTimeout(() => {
                setupEventListeners();
                updatePreview();
            }, 100);
        }
    </script>
</body>
</html>
