'use client';

import { ReceiptData } from '@/app/page';

interface ReceiptPreviewProps {
  receiptData: ReceiptData;
}

export function ReceiptPreview({ receiptData }: ReceiptPreviewProps) {
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ar-IQ').format(num);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-IQ', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-white border-2 border-gray-300 p-8 max-w-md mx-auto" style={{ fontFamily: 'Arial, sans-serif' }}>
      {/* Header */}
      <div className="text-center mb-6 border-b-2 border-gray-800 pb-4">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">هيمن كروب</h1>
        <h2 className="text-lg font-semibold text-gray-600">HEMEN GROUP</h2>
        <p className="text-sm text-gray-500 mt-2">إيصال صرف</p>
      </div>

      {/* Receipt Info */}
      <div className="mb-6 space-y-3">
        <div className="flex justify-between items-center">
          <span className="font-semibold text-gray-700">رقم الإيصال:</span>
          <span className="text-gray-800">{receiptData.receiptNumber || '---'}</span>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="font-semibold text-gray-700">التاريخ:</span>
          <span className="text-gray-800">{formatDate(receiptData.date)}</span>
        </div>
        
        {receiptData.projectName && (
          <div className="flex justify-between items-center">
            <span className="font-semibold text-gray-700">المشروع:</span>
            <span className="text-gray-800">{receiptData.projectName}</span>
          </div>
        )}
      </div>

      {/* Amount Details */}
      <div className="mb-6 bg-gray-50 p-4 rounded-lg">
        <h3 className="font-bold text-gray-800 mb-3 text-center">تفاصيل المبلغ</h3>
        
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-gray-700">المبلغ بالدولار:</span>
            <span className="font-semibold text-green-600">
              ${formatNumber(receiptData.dollarAmount || 0)}
            </span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-gray-700">سعر الصرف:</span>
            <span className="font-semibold text-blue-600">
              {formatNumber(receiptData.exchangeRate || 0)} د.ع
            </span>
          </div>
          
          <div className="border-t pt-2">
            <div className="flex justify-between items-center">
              <span className="text-gray-700">المبلغ بالدينار:</span>
              <span className="font-bold text-red-600 text-lg">
                {formatNumber(receiptData.iraqiAmount || 0)} د.ع
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Description */}
      {receiptData.description && (
        <div className="mb-6">
          <h4 className="font-semibold text-gray-700 mb-2">الوصف:</h4>
          <p className="text-gray-600 text-sm bg-gray-50 p-3 rounded">
            {receiptData.description}
          </p>
        </div>
      )}

      {/* Signatures */}
      <div className="mt-8 pt-6 border-t-2 border-gray-300">
        <div className="grid grid-cols-2 gap-8">
          <div className="text-center">
            <div className="border-b border-gray-400 mb-2 h-8"></div>
            <p className="text-sm text-gray-600">المستلم</p>
            <p className="text-xs text-gray-500">Receiver</p>
          </div>
          
          <div className="text-center">
            <div className="border-b border-gray-400 mb-2 h-8"></div>
            <p className="text-sm text-gray-600">المسؤول</p>
            <p className="text-xs text-gray-500">Authorized</p>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-6 text-center text-xs text-gray-400 border-t pt-4">
        <p>هذا الإيصال صالح للأرشفة والمراجعة</p>
        <p className="mt-1">This receipt is valid for archiving and review</p>
      </div>

      {/* Duplicate Notice */}
      <div className="mt-8 pt-6 border-t-2 border-dashed border-gray-400">
        <div className="text-center text-sm text-gray-500 mb-4">
          <p>نسخة للأرشفة - Archive Copy</p>
        </div>
        
        {/* Duplicate Receipt */}
        <div className="bg-gray-50 p-6 rounded-lg">
          <div className="text-center mb-4">
            <h1 className="text-xl font-bold text-gray-700 mb-1">هيمن كروب</h1>
            <h2 className="text-base font-semibold text-gray-500">HEMEN GROUP</h2>
            <p className="text-xs text-gray-400 mt-1">إيصال صرف - نسخة الأرشفة</p>
          </div>

          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>رقم الإيصال:</span>
              <span>{receiptData.receiptNumber || '---'}</span>
            </div>
            <div className="flex justify-between">
              <span>التاريخ:</span>
              <span>{formatDate(receiptData.date)}</span>
            </div>
            {receiptData.projectName && (
              <div className="flex justify-between">
                <span>المشروع:</span>
                <span>{receiptData.projectName}</span>
              </div>
            )}
            <div className="flex justify-between font-semibold">
              <span>المبلغ:</span>
              <span>${formatNumber(receiptData.dollarAmount || 0)} = {formatNumber(receiptData.iraqiAmount || 0)} د.ع</span>
            </div>
          </div>

          <div className="mt-4 pt-3 border-t border-gray-300">
            <div className="grid grid-cols-2 gap-4 text-center text-xs">
              <div>
                <div className="border-b border-gray-300 mb-1 h-6"></div>
                <p>المستلم</p>
              </div>
              <div>
                <div className="border-b border-gray-300 mb-1 h-6"></div>
                <p>المسؤول</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
