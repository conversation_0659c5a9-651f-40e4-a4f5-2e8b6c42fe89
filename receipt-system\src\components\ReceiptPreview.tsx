'use client';

import { ReceiptData } from '@/app/page';

interface ReceiptPreviewProps {
  receiptData: ReceiptData;
}

export function ReceiptPreview({ receiptData }: ReceiptPreviewProps) {
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ar-IQ').format(num);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-IQ', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-white border-2 border-gray-300 p-8 max-w-md mx-auto shadow-lg" style={{ fontFamily: 'Arial, sans-serif' }}>
      {/* Header with Logo */}
      <div className="text-center mb-6 border-b-2 border-blue-600 pb-4">
        <div className="flex items-center justify-center mb-3">
          <img
            src="/logo.jpg"
            alt="هيمن كروب"
            className="w-16 h-16 rounded-full object-cover border-2 border-blue-600 mr-3"
            onError={(e) => {
              e.currentTarget.style.display = 'none';
            }}
          />
          <div>
            <h1 className="text-2xl font-bold text-blue-800 mb-1">هيمن كروب</h1>
            <h2 className="text-lg font-semibold text-blue-600">HEMEN GROUP</h2>
          </div>
        </div>
        <div className="bg-blue-50 px-4 py-2 rounded-lg">
          <p className="text-sm text-blue-700 font-semibold">إيصال صرف مالي</p>
          <p className="text-xs text-blue-600">Financial Receipt</p>
        </div>
      </div>

      {/* Receipt Info */}
      <div className="mb-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
        <h3 className="text-lg font-bold text-gray-800 mb-3 text-center border-b border-gray-300 pb-2">
          معلومات الإيصال
        </h3>
        <div className="space-y-3">
          <div className="flex justify-between items-center bg-white p-2 rounded border-l-4 border-blue-500">
            <span className="font-semibold text-blue-700">رقم الإيصال:</span>
            <span className="text-gray-800 font-mono bg-blue-50 px-2 py-1 rounded">{receiptData.receiptNumber || '---'}</span>
          </div>

          <div className="flex justify-between items-center bg-white p-2 rounded border-l-4 border-green-500">
            <span className="font-semibold text-green-700">التاريخ:</span>
            <span className="text-gray-800 font-medium">{formatDate(receiptData.date)}</span>
          </div>

          {receiptData.projectName && (
            <div className="flex justify-between items-center bg-white p-2 rounded border-l-4 border-purple-500">
              <span className="font-semibold text-purple-700">المشروع:</span>
              <span className="text-gray-800 font-medium">{receiptData.projectName}</span>
            </div>
          )}
        </div>
      </div>

      {/* Amount Details */}
      <div className="mb-6 bg-gradient-to-br from-blue-50 to-green-50 p-5 rounded-lg border-2 border-blue-200">
        <h3 className="font-bold text-blue-800 mb-4 text-center text-lg border-b-2 border-blue-300 pb-2">
          💰 تفاصيل المبلغ المالي
        </h3>

        <div className="space-y-3">
          <div className="flex justify-between items-center bg-white p-3 rounded-lg shadow-sm border-l-4 border-green-500">
            <span className="text-gray-700 font-medium">💵 المبلغ بالدولار:</span>
            <span className="font-bold text-green-700 text-lg bg-green-50 px-3 py-1 rounded">
              ${formatNumber(receiptData.dollarAmount || 0)}
            </span>
          </div>

          <div className="flex justify-between items-center bg-white p-3 rounded-lg shadow-sm border-l-4 border-blue-500">
            <span className="text-gray-700 font-medium">📊 سعر الصرف:</span>
            <span className="font-bold text-blue-700 text-lg bg-blue-50 px-3 py-1 rounded">
              {formatNumber(receiptData.exchangeRate || 0)} د.ع
            </span>
          </div>

          <div className="bg-gradient-to-r from-red-50 to-orange-50 p-4 rounded-lg border-2 border-red-300 shadow-md">
            <div className="flex justify-between items-center">
              <span className="text-red-800 font-bold text-lg">🏛️ المبلغ الإجمالي بالدينار:</span>
              <span className="font-bold text-red-700 text-xl bg-red-100 px-4 py-2 rounded-lg border border-red-300">
                {formatNumber(receiptData.iraqiAmount || 0)} د.ع
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Description */}
      {receiptData.description && (
        <div className="mb-6">
          <h4 className="font-semibold text-gray-700 mb-2">الوصف:</h4>
          <p className="text-gray-600 text-sm bg-gray-50 p-3 rounded">
            {receiptData.description}
          </p>
        </div>
      )}

      {/* Signatures */}
      <div className="mt-8 pt-6 border-t-2 border-blue-300 bg-gray-50 p-4 rounded-lg">
        <h4 className="text-center font-bold text-gray-800 mb-4">التوقيعات والاعتماد</h4>
        <div className="grid grid-cols-2 gap-8">
          <div className="text-center bg-white p-3 rounded-lg border border-gray-200">
            <div className="border-b-2 border-gray-400 mb-3 h-12 bg-gray-50 rounded"></div>
            <p className="text-sm font-semibold text-gray-700">✍️ المستلم</p>
            <p className="text-xs text-gray-500">Receiver</p>
            <p className="text-xs text-gray-400 mt-1">التوقيع والتاريخ</p>
          </div>

          <div className="text-center bg-white p-3 rounded-lg border border-gray-200">
            <div className="border-b-2 border-gray-400 mb-3 h-12 bg-gray-50 rounded"></div>
            <p className="text-sm font-semibold text-gray-700">👨‍💼 المسؤول</p>
            <p className="text-xs text-gray-500">Authorized</p>
            <p className="text-xs text-gray-400 mt-1">التوقيع والختم</p>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-6 text-center bg-blue-50 p-3 rounded-lg border border-blue-200">
        <p className="text-sm font-semibold text-blue-800">✅ هذا الإيصال صالح للأرشفة والمراجعة</p>
        <p className="text-xs text-blue-600 mt-1">This receipt is valid for archiving and review</p>
        <p className="text-xs text-gray-500 mt-2">تم الإنشاء بواسطة نظام هيمن كروب المالي</p>
      </div>

      {/* Duplicate Notice */}
      <div className="mt-8 pt-6 border-t-2 border-dashed border-blue-400">
        <div className="text-center text-sm bg-yellow-50 border border-yellow-300 rounded-lg p-3 mb-4">
          <p className="font-semibold text-yellow-800">📋 نسخة للأرشفة - Archive Copy</p>
          <p className="text-xs text-yellow-600 mt-1">يرجى الاحتفاظ بهذه النسخة للمراجعة</p>
        </div>

        {/* Duplicate Receipt */}
        <div className="bg-gradient-to-br from-gray-50 to-blue-50 p-6 rounded-lg border-2 border-gray-300 shadow-inner">
          <div className="text-center mb-4 bg-white p-3 rounded-lg border border-gray-200">
            <div className="flex items-center justify-center mb-2">
              <img
                src="/logo.jpg"
                alt="هيمن كروب"
                className="w-8 h-8 rounded-full object-cover border border-gray-300 mr-2"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
              <div>
                <h1 className="text-lg font-bold text-gray-700">هيمن كروب</h1>
                <h2 className="text-sm font-semibold text-gray-500">HEMEN GROUP</h2>
              </div>
            </div>
            <p className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">إيصال صرف - نسخة الأرشفة</p>
          </div>

          <div className="space-y-2 text-sm bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex justify-between border-b border-gray-100 pb-1">
              <span className="font-medium text-gray-600">رقم الإيصال:</span>
              <span className="font-mono bg-gray-100 px-2 py-1 rounded text-xs">{receiptData.receiptNumber || '---'}</span>
            </div>
            <div className="flex justify-between border-b border-gray-100 pb-1">
              <span className="font-medium text-gray-600">التاريخ:</span>
              <span className="text-gray-800">{formatDate(receiptData.date)}</span>
            </div>
            {receiptData.projectName && (
              <div className="flex justify-between border-b border-gray-100 pb-1">
                <span className="font-medium text-gray-600">المشروع:</span>
                <span className="text-gray-800">{receiptData.projectName}</span>
              </div>
            )}
            <div className="flex justify-between font-semibold bg-blue-50 p-2 rounded border border-blue-200">
              <span className="text-blue-800">المبلغ الإجمالي:</span>
              <span className="text-blue-900">${formatNumber(receiptData.dollarAmount || 0)} = {formatNumber(receiptData.iraqiAmount || 0)} د.ع</span>
            </div>
          </div>

          <div className="mt-4 pt-3 border-t border-gray-300 bg-white rounded-lg p-3">
            <div className="grid grid-cols-2 gap-4 text-center text-xs">
              <div>
                <div className="border-b border-gray-300 mb-2 h-6 bg-gray-50 rounded"></div>
                <p className="font-medium text-gray-600">المستلم</p>
              </div>
              <div>
                <div className="border-b border-gray-300 mb-2 h-6 bg-gray-50 rounded"></div>
                <p className="font-medium text-gray-600">المسؤول</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
