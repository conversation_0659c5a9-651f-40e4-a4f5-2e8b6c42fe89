# 🏢 نظام إدارة الإيصالات المالية - هيمن كروب

## 📋 نظرة عامة

نظام احترافي لإدارة الإيصالات المالية وتحويل العملات، مصمم خصيصاً لشركة هيمن كروب. يوفر النظام واجهة سهلة الاستخدام لإنشاء وطباعة الإيصالات المالية مع حساب تلقائي لتحويل العملات.

## ✨ الميزات الرئيسية

### 💰 إدارة الإيصالات
- **إنشاء إيصالات احترافية** مع تصميم عصري وأنيق
- **أرقام إيصالات تلقائية** بنظام تاريخي منظم
- **معاينة مباشرة** للإيصال قبل الطباعة
- **طباعة مزدوجة** (نسخة أصلية + نسخة أرشفة)

### 🔄 تحويل العملات
- **حساب تلقائي** للتحويل بين الدولار والدينار العراقي
- **سعر صرف قابل للتعديل** حسب السوق
- **طريقتان للحساب**: من الدولار للدينار أو العكس
- **عرض واضح** للمبالغ بكلا العملتين

### 🎨 التصميم والواجهة
- **تصميم احترافي** مع ألوان متدرجة وظلال عصرية
- **لوجو الشركة** مدمج في الإيصالات
- **واجهة عربية** كاملة مع دعم اللغة الإنجليزية
- **تصميم متجاوب** يعمل على جميع الأجهزة

### 🖨️ الطباعة والأرشفة
- **طباعة عالية الجودة** مع تنسيق مثالي
- **نسخة أرشفة مصغرة** للحفظ
- **تحسين خاص للطباعة** مع إخفاء العناصر غير المطلوبة

## 🚀 التشغيل والاستخدام

### متطلبات النظام
- Node.js 18+
- npm أو yarn أو pnpm

### التثبيت والتشغيل

```bash
# تثبيت المتطلبات
npm install

# تشغيل الخادم المحلي
npm run dev

# أو باستخدام yarn
yarn dev

# أو باستخدام pnpm
pnpm dev
```

افتح [http://localhost:3000](http://localhost:3000) في المتصفح لرؤية النظام.

## 📁 هيكل المشروع

```
receipt-system/
├── src/
│   ├── app/
│   │   ├── page.tsx          # الصفحة الرئيسية
│   │   ├── layout.tsx        # تخطيط التطبيق
│   │   └── globals.css       # الأنماط العامة
│   └── components/
│       ├── ReceiptForm.tsx   # نموذج إدخال البيانات
│       └── ReceiptPreview.tsx # معاينة الإيصال
├── public/
│   └── logo.jpg             # لوجو الشركة
└── package.json
```

## 🎯 كيفية الاستخدام

### 1. إدخال البيانات
- **رقم الإيصال**: يتم إنشاؤه تلقائياً أو يمكن تعديله
- **التاريخ**: التاريخ الحالي افتراضياً
- **اسم المشروع**: اختياري
- **طريقة الحساب**: اختر بين الدولار→دينار أو دينار→دولار
- **سعر الصرف**: قابل للتعديل حسب السوق
- **المبلغ**: أدخل في العملة المحددة وسيتم الحساب تلقائياً
- **الوصف**: تفاصيل إضافية عن العملية

### 2. المعاينة والطباعة
- **معاينة مباشرة** للإيصال في الجانب الأيمن
- **زر الطباعة** لطباعة النسخة النهائية
- **نسختان**: الأصل للعميل ونسخة للأرشفة

## 🛠️ التقنيات المستخدمة

- **Next.js 15** - إطار عمل React المتقدم
- **TypeScript** - للكتابة الآمنة والمنظمة
- **Tailwind CSS** - للتصميم السريع والمرن
- **React Hooks** - لإدارة الحالة والتفاعل

## 🎨 التخصيص

### تغيير اللوجو
استبدل ملف `public/logo.jpg` بلوجو شركتك

### تعديل الألوان
عدّل الألوان في ملف `globals.css` أو في مكونات Tailwind

### إضافة حقول جديدة
عدّل واجهة `ReceiptData` في `page.tsx` وأضف الحقول في المكونات

## 📱 الاستجابة والتوافق

- **متوافق مع جميع المتصفحات** الحديثة
- **تصميم متجاوب** للهواتف والأجهزة اللوحية
- **دعم كامل للعربية** مع اتجاه RTL
- **طباعة محسنة** لجميع أحجام الورق

## 🔧 التطوير والصيانة

### إضافة ميزات جديدة
1. أضف الحقول المطلوبة في واجهة `ReceiptData`
2. حدّث نموذج الإدخال في `ReceiptForm.tsx`
3. حدّث معاينة الإيصال في `ReceiptPreview.tsx`

### النشر
```bash
# بناء النسخة النهائية
npm run build

# تشغيل النسخة النهائية
npm start
```

## 📞 الدعم والتواصل

للدعم الفني أو الاستفسارات:
- **الشركة**: هيمن كروب
- **النظام**: نظام إدارة الإيصالات المالية v2.0

## 📄 الترخيص

© 2024 هيمن كروب - جميع الحقوق محفوظة
HEMEN GROUP - All Rights Reserved
